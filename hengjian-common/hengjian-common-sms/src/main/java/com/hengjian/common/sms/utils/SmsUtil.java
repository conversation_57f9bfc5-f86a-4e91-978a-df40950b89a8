package com.hengjian.common.sms.utils;

import com.hengjian.common.sms.config.properties.SmsProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.dromara.sms4j.provider.enumerate.SupplierType;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@RequiredArgsConstructor
public class SmsUtil {

    private static SmsProperties smsProperties;

    public SmsUtil(SmsProperties smsProperties) {
    }

    @Autowired
    public void setSmsProperties(SmsProperties smsProperties) {
        SmsUtil.smsProperties = smsProperties;
    }

    public static SmsBlend createSmsBlend() {
        String supplierTypeName = smsProperties.getSupplierType();
        SupplierType supplierType = SupplierType.valueOf(supplierTypeName.toUpperCase());
        return SmsFactory.createSmsBlend(supplierType);
    }

    public static SmsBlend createRestrictedSmsBlend() {
        String supplierTypeName = smsProperties.getSupplierType();
        SupplierType supplierType = SupplierType.valueOf(supplierTypeName.toUpperCase());
        return SmsFactory.getRestrictedSmsBlend(supplierType);
    }
}
