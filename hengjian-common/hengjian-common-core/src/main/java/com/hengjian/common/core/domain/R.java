package com.hengjian.common.core.domain;

import com.hengjian.common.core.constant.HttpStatus;
import com.hengjian.common.core.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 响应信息主体
 *
 * <AUTHOR> Li
 */
@Data
@NoArgsConstructor
public class R<T> implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = 200;

    /**
     * 失败
     */
    public static final int FAIL = 500;

    private int code;

    private String subCode;

    private String msg;

    private String msgHtml;

    private T data;

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, "操作成功");
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功");
    }

    public static <T> R<T> ok(RStatusCodeBase statusCodeBase) {
        return restResult(null, SUCCESS, statusCodeBase);
    }

    public static <T> R<T> ok(RStatusCodeBase statusCodeBase, T data) {
        return restResult(data, SUCCESS, statusCodeBase);
    }

    public static <T> R<T> ok(String msg) {
        return restResult(null, SUCCESS, msg);
    }

    public static <T> R<T> ok(String msg, T data) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> okHtml(String msg) {
        return restResultHtml(null, SUCCESS, msg);
    }

    public static <T> R<T> fail() {
        return restResult(null, FAIL, "操作失败");
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> fail(String msg, String subCode) {
        return restResult(null, FAIL, subCode, msg);
    }
    public static <T> R<T> fail(T data,int code,String msg, String subCode ) {
        return restResult(data, code, subCode, msg);
    }

    public static <T> R<T> failHtml(String msg) {
        return restResultHtml(null, FAIL, msg);
    }

    /**
     * 使用枚举错误码请注意，请确保枚举已经实现RStatusCodeBase接口
     * @param statusCodeBase
     * @param <T>
     * @return
     */
    public static <T> R<T> fail(RStatusCodeBase statusCodeBase) {
        return restResult(null, FAIL, statusCodeBase);
    }

    public static <T> R<T> fail(T data) {
        return restResult(data, FAIL, "操作失败");
    }

    public static <T> R<T> fail(Enum<?> respEnum, T data) {
        return restResult(data, FAIL, "操作失败");
    }

    public static <T> R<T> fail(String msg, T data) {
        return restResult(data, FAIL, msg);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static <T> R<T> warn(String msg) {
        return restResult(null, HttpStatus.WARN, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <T> R<T> warn(String msg, T data) {
        return restResult(data, HttpStatus.WARN, msg);
    }

    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> r = new R<>();
        r.setCode(code);
        r.setData(data);
        r.setMsg(msg);
        return r;
    }

    private static <T> R<T> restResult(T data, int code, String subCode, String msg) {
        R<T> r = new R<>();
        r.setCode(code);
        r.setSubCode(subCode);
        r.setData(data);
        r.setMsg(msg);
        return r;
    }

    private static <T> R<T> restResultHtml(T data, int code, String msgHtml) {
        R<T> r = new R<>();
        r.setCode(code);
        r.setData(data);
        r.setMsg(msgHtml);
        r.setMsgHtml(msgHtml);
        return r;
    }

    private static <T> R<T> restResult(T data, int code, RStatusCodeBase statusCodeBase) {
        R<T> r = new R<>();
        r.setData(data);
        r.setCode(code);

        try {
            r.setSubCode(statusCodeBase.getSubCode());

            String message = statusCodeBase.getMessage();
            Object[] args = statusCodeBase.getArgs();
            if (args != null && args.length > 0) {
                message = StringUtils.format(message, args);
            }
            r.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            r.setMsg("Unknown error!");
        }
        return r;
    }

    public static <T> Boolean isError(R<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(R<T> ret) {
        return R.SUCCESS == ret.getCode();
    }
}
