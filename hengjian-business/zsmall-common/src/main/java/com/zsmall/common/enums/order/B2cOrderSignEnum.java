package com.zsmall.common.enums.order;

import com.zsmall.common.annotaion.BaseEnum;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 17:06
 */
public enum B2cOrderSignEnum implements BaseEnum<Integer> {

    baseInfo(0, "-"),
    celebrity(1,"红人订单"),
    sample(2,"样品订单"),
    reshipment(3,"补发订单"),
    addition(4,"礼品订单"),
    disassemblyParent(5,"拆解父订单"),
    disassemblyChild(6,"拆解子订单"),
    ;

    private final int value;

    private final String name;

    B2cOrderSignEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * Return the integer value of this status code.
     */
    public Integer value() {
        return this.value;
    }

    /**
     * Return the name of this status code.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Return a string representation of this status code.
     */
    @Override
    public String toString() {
        return Integer.toString(this.value);
    }

    /**
     *
     * @param code
     * @return
     */
    public static String nameOf(int code) {
        for (B2cOrderSignEnum item : values()) {
            if (item.value == code) {
                return item.name;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    /**
     *
     * @param code
     * @return
     */
    public static B2cOrderSignEnum valueOf(int code) {
        for (B2cOrderSignEnum item : values()) {
            if (item.value == code) {
                return item;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
