package com.zsmall.common.enums.productSku;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024年12月27日  18:37
 * @description: 库存标识枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DropShippingStockAvailableEnum {

    /**
     * 自提
     */
    SINGLE(0),

    /**
     * 代发
     */
    NOT_SINGLE(1),
    ;

    private int code;

    public static DropShippingStockAvailableEnum fromCode(int code) {
        DropShippingStockAvailableEnum results = null;
        for (DropShippingStockAvailableEnum type : DropShippingStockAvailableEnum.values()) {
            if (ObjectUtil.equals(type.code, code)) {
                results = type;
            }
        }
        return results;
    }
}
