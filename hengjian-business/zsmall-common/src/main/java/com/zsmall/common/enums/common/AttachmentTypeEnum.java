package com.zsmall.common.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 附件类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
public enum AttachmentTypeEnum implements IEnum<String> {

    /**
     * 文件
     */
    File,

    /**
     * 压缩包
     */
    Zip,

    /**
     * 图片
     */
    Image,

    /**
     * 视频
     */
    Video,
    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
