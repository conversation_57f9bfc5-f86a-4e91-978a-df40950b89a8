package com.zsmall.common.enums.bill;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 账单摘要字段类型枚举
 * <AUTHOR>
 * @date 2022/11/29
 */
public enum AbstractFieldTypeEnum implements IEnum<String> {

  /**
   * 产品总金额
   */
  Total_ProductAmount("产品总金额", "Product Total Amount"),

  /**
   * 操作费总金额
   */
  Total_OperationFee("操作费总金额", "Operation Fee Total Amount"),

  /**
   * 尾程派送费总金额
   */
  Total_FinalDeliveryFee("尾程派送费总金额", "Final Delivery Fee Total Amount"),

  /**
   * 退款总金额
   */
  Total_RefundAmount("退款总金额", "Refund Total Amount"),

  /**
   * 活动返款总金额
   */
  Total_ActivityReturnRebate("活动返款总金额", "Activity Return Rebate Total Amount"),

  /**
   * 活动仓储费总金额
   */
  Total_ActivityStorageFee("仓储费总金额", "Storage Fee Total Amount"),

  /**
   * 活动违约金总金额
   */
  Total_ActivityPenaltyFee("活动订金总金额", "Penalty Fee Total Amount"),

  /**
   * 清货打托费总金额
   */
  Total_PalletFee("清货打托费总金额", "Liquidation Pallet Fee Total Amount"),

  /**
   * 清货佣金总金额
   */
  Total_CommissionFee("清货佣金总金额", "Liquidation Commission Total Amount"),

  /**
   * 平台扣款总金额
   */
  Total_PlatformDeduction("平台扣款总金额", "Platform Deduction Total Amount"),

  /**
   * 平台充值总金额
   */
  Total_PlatformRecharge("平台充值总金额", "Platform Recharge Total Amount"),

  ;

  private String zh_CN;
  private String en_US;

  AbstractFieldTypeEnum(String zh_CN, String en_US) {
    this.zh_CN = zh_CN;
    this.en_US = en_US;
  }

  public String getZh_CN() {
    return zh_CN;
  }

  public String getEn_US() {
    return en_US;
  }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
