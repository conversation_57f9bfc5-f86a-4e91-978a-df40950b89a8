CREATE TABLE tik_tok_order (

id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
tik_tok_order_id VARCHAR(1000),
buyer_email VARCHAR(500),
buyer_message VARCHAR(1000),
cancel_order_sla_time datetime,
cancel_reason VARCHAR(1000),
cancel_time datetime,
cancellation_initiator VARCHA<PERSON>(1000),
collection_due_time datetime,
collection_time datetime,
cpf VARCHAR(256),
create_time datetime,
delivery_due_time datetime,
delivery_option_id VARCHAR(500),
delivery_option_name VARCHAR(1000),
delivery_option_required_delivery_time datetime,
delivery_sla_time datetime,
delivery_time datetime,
fulfillment_type VARCHAR(1000),
has_updated_recipient_address VARCHAR(1000),
is_buyer_request_cancel VARCHAR(1000),
is_cod VARCHAR(1000),
is_on_hold_order VARCHAR(1000),
is_sample_order VARCHAR(1000),

line_item_id VARCHAR(1000),

need_upload_invoice VARCHAR(1000),

package_id VARCHAR(1000),

paid_time datetime,
payment VARCHAR(1000),
payment_method_name VA<PERSON>HA<PERSON>(1000),
recipient_address VARCHAR(1000),
request_cancel_time datetime,
rts_sla_time datetime,
rts_time datetime,
seller_note VARCHAR(1000),
shipping_due_time datetime,
shipping_provider VARCHAR(1000),
shipping_provider_id VARCHAR(1000),
shipping_type VARCHAR(256),
slit_or_combine_tag VARCHAR(1000),
status VARCHAR(256),
tracking_number VARCHAR(1000),
tts_sla_time datetime,
update_time datetime,
user_id VARCHAR(500),
warehouse_id VARCHAR(500));
