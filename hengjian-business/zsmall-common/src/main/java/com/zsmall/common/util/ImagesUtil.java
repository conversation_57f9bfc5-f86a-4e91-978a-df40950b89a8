package com.zsmall.common.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.properties.FileProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/21 15:10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ImagesUtil {

    private final FileProperties fileProperties;
    private static final OkHttpClient client = new OkHttpClient();

    /**
     * 功能描述：下载图像  改同步
     *
     * @param image 形象
     * @return {@link SysOssVo }
     * <AUTHOR>
     * @date 2024/06/12
     */
    public SysOssVo downloadImage(String image) {
        // 处理图片
        File file = null;
        SysOssVo sysOssVo=null;
        // 因为服务器在国内,所以下载图片时需要代理
        String imageUrl = null;

        try {
            ObjectMapper objectMapper = new ObjectMapper();

            // 创建一个空的ObjectNode
            ObjectNode node = objectMapper.createObjectNode();

            // 手动添加属性到ObjectNode
            node.put("url", image);

            String json;
            // 将ObjectNode转换为JSON字符串
            try {
                json = objectMapper.writeValueAsString(node);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String result = OkHttpUtil.post("http://8.210.198.240:11111/api/v1/distribution_oss", json);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String status = jsonObject.getString("status");
            imageUrl = jsonObject.getString("url");
            if(!"Success".equals(status)){
                throw new Exception("下载图片失败");
            }

            String tempSavePath = fileProperties.getTempSavePath();
            String fileName = UUID.fastUUID().toString(true) + "." + "jpeg";
            String tempPath = tempSavePath + File.separator + "tiktokImport" + File.separator + fileName;
            file = FileUtil.newFile(tempPath);
            // 下载图片
            HttpUtil.downloadFile(imageUrl, file, 5000);

            BufferedInputStream inputStream = FileUtil.getInputStream(file);
            sysOssVo = SystemEventUtils.uploadFile(inputStream, fileName);

        } catch (Exception e) {
            log.error("下载图片失败 image = {}，原因 {}", imageUrl, e.getMessage(), e);
        } finally {
            FileUtil.del(file);
        }

        return sysOssVo;
    }

    public static String downloadPdf(String url) {
        // 处理图片
        File file = null;
        SysOssVo sysOssVo=null;
        // 因为服务器在国内,所以下载图片时需要代理
        String imageUrl = null;

        try {
            ObjectMapper objectMapper = new ObjectMapper();

            // 创建一个空的ObjectNode
            ObjectNode node = objectMapper.createObjectNode();

            // 手动添加属性到ObjectNode
            node.put("url", url);

            String json;
            // 将ObjectNode转换为JSON字符串
            try {
                json = objectMapper.writeValueAsString(node);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            log.info("tom下载图片参数：{}", json);
            String result = OkHttpUtil.post("http://8.210.198.240:11111/api/v1/distribution_oss", json);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String status = jsonObject.getString("status");
            imageUrl = jsonObject.getString("url");
            log.info("tom下载图片返回结果：{}", result);
            if(!"Success".equalsIgnoreCase(status)){
                throw new Exception("下载图片失败");
            }


        } catch (Exception e) {
            log.error("下载图片失败 image = {}，原因 {}", imageUrl, e.getMessage(), e);
        }
        return imageUrl;
    }
    public static InputStream downloadPdfAsStream(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();

        // 设置请求方法
        httpURLConnection.setRequestMethod("GET");

        // 设置连接超时时间
        httpURLConnection.setConnectTimeout(10000);

        // 设置读取超时时间
        httpURLConnection.setReadTimeout(10000);

        // 发送请求
        httpURLConnection.connect();

        // 检查响应码
        int responseCode = httpURLConnection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 获取输入流
            return new BufferedInputStream(httpURLConnection.getInputStream());
        } else {
            throw new Exception("Failed to download file: HTTP error code : " + responseCode);
        }
    }

    public static InputStream downloadPdfAsStreamV2(String urlString) throws IOException {
        Request request = new Request.Builder()
            .url(urlString)
            .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            // 获取PDF文件的InputStream
            ResponseBody body = response.body();
            if (body != null) {
                InputStream inputStream = body.byteStream();
                return new BufferedInputStream(inputStream);
            }
        }
        return null;
    }
}
