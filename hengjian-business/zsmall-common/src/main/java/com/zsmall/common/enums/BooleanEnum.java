package com.zsmall.common.enums;

import com.zsmall.common.annotaion.BaseEnum;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 16:14
 */
public enum BooleanEnum implements BaseEnum<Integer> {
    N(0,"false"),
    Y(1,"true"),
    ;
    private final int value;

    private final String name;

    BooleanEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * Return the integer value of this status code.
     */
    public Integer value() {
        return this.value;
    }

    /**
     * Return the name of this status code.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Return a string representation of this status code.
     */
    @Override
    public String toString() {
        return Integer.toString(this.value);
    }

    /**
     *
     * @param code
     * @return
     */
    public static String nameOf(int code) {
        for (BooleanEnum item : values()) {
            if (item.value == code) {
                return item.name;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    /**
     *
     * @param code
     * @return
     */
    public static BooleanEnum valueOf(int code) {
        for (BooleanEnum item : values()) {
            if (item.value == code) {
                return item;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
