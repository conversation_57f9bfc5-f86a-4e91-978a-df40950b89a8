package com.zsmall.common.util;

import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.hengjian.common.core.utils.MessageUtils;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.function.ZFunction;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Excel信息构建者
 *
 * <AUTHOR>
 * @date 2023/7/15
 */
@Slf4j
public class ExcelMsgBuilder<T extends ExcelBaseDTO> {

    private static Map<Class, SerializedLambda> CLASS_LAMDBA_CACHE = new ConcurrentHashMap<>();
    private static final List<Locale> supportLang = new ArrayList<>() {
        {
            add(Locale.SIMPLIFIED_CHINESE);
            add(Locale.US);
        }
    };

    private Row titleRow;
    private ReadRowHolder readRowHolder;
    private Class<T> entityClass;
    private Map<String, String> titleMap;

    /**
     * 信息通用前缀
     */
    private String msgPrefix = new String("");
    /**
     * 信息通用后缀
     */
    private String msgSuffix = new String("");

    /**
     * 当前的行号（for循环表格数据时，每到新的一行都要重新赋值，以保证展示的行号正确）
     */
    private Integer nowShowRow;

//        public ExcelMsgBuilder(Row titleRow, Class<T> beanClass) {
//            this.titleRow = titleRow;
//            this.entityClass = beanClass;
//
//            titleMap = new HashMap<>();
//            Field[] declaredFields = entityClass.getDeclaredFields();
//            for (int i = 0; i < declaredFields.length; i++) {
//                log.info("当前字段：" + declaredFields[i].getName());
//                String fieldName = declaredFields[i].getName();
//                Cell cell = titleRow.getCell(i);
//                String titleName = cell.getStringCellValue();
//                titleMap.put(fieldName, titleName);
//            }
//        }
    // tag lty
    public ExcelMsgBuilder(Row titleRow, Class<T> beanClass) {
        this.titleRow = titleRow;
        this.entityClass = beanClass;

        titleMap = new HashMap<>();
        if (titleRow != null) {
            Field[] declaredFields = entityClass.getDeclaredFields();
            // 确保索引在有效范围内
            for (int i = 0; i < declaredFields.length && i < titleRow.getLastCellNum(); i++) {
                String fieldName = declaredFields[i].getName();
                // 获取单元格
                Cell cell = titleRow.getCell(i);
                // 检查单元格是否为 null
                if (cell != null) {
                    // 获取单元格的值
                    String titleName = cell.getStringCellValue();
                    titleMap.put(fieldName, titleName);
                } else {
                    // 处理单元格为 null 的情况，例如：记录一个错误或跳过该字段
                    log.info("Cell at index " + i + " is null!");
//                    throw new RuntimeException("Cell at index " + i + " is null!");
                }
            }
        } else {
            // 处理 titleRow 为 null 的情况
            log.info("Title row is null!");
        }
    }
    public ExcelMsgBuilder(ReadRowHolder readRowHolder, Class<T> beanClass) {
        this.entityClass = beanClass;
        this.readRowHolder = readRowHolder;
        titleMap = new HashMap<>();
        if (readRowHolder != null) {
            // 获取当前行的单元格列表（List<ReadCellData<?>>）
            // 获取单元格的列索引集合（Map<Integer, ReadCellData<?>>）
            Map<Integer, com.alibaba.excel.metadata.Cell> cellMap = readRowHolder.getCellMap();

// 计算最后一个单元格的列索引加一
            int lastCellNum = cellMap.keySet().stream()
                                     .mapToInt(Integer::intValue)
                                     .max()
                                     .orElse(-1) + 1;

            System.out.println("总列数（LastCellNum）: " + lastCellNum);

            Field[] declaredFields = entityClass.getDeclaredFields();
            // 确保索引在有效范围内
            for (int i = 0; i < declaredFields.length && i < lastCellNum; i++) {
                String fieldName = declaredFields[i].getName();
                // 获取单元格
                // 获取列索引为 i 的单元格数据
                com.alibaba.excel.metadata.data.CellData CellData = (com.alibaba.excel.metadata.data.CellData) readRowHolder.getCellMap().get(i);

                // 检查单元格是否为 null
                if (CellData != null) {
                    // 获取单元格的值
                    String titleName = CellData.getStringValue();
                    titleMap.put(fieldName, titleName);
                } else {
                    // 处理单元格为 null 的情况，例如：记录一个错误或跳过该字段
                    log.info("Cell at index " + i + " is null!");
                }
            }
        } else {
            // 处理 titleRow 为 null 的情况
            log.info("Title row is null!");
        }
    }

    public ExcelMsgBuilder setMsgPrefix(String msgPrefix) {
        this.msgPrefix = msgPrefix;
        return this;
    }

    public ExcelMsgBuilder setMsgSuffix(String msgSuffix) {
        this.msgSuffix = msgSuffix;
        return this;
    }

    public void setNowShowRow(Integer nowShowRow) {
        this.nowShowRow = nowShowRow;
    }

    /**
     * 构建信息（没有行号与列，适用于全局错误）
     *
     * @param messageEnum
     * @return
     */
    public LocaleMessage build(ExcelMessageEnum messageEnum) {
        String messageCode = messageEnum.getMessageCode();
        Object[] args = messageEnum.getArgs();
        Map<String, StringBuilder> langBuilderMap = new HashMap<>();
        for (Locale locale : supportLang) {
            StringBuilder localeMsg = new StringBuilder(msgPrefix).append(MessageUtils.message(locale, messageCode, args))
                                                                  .append(msgSuffix);
            langBuilderMap.put(locale.toString(), localeMsg);
        }
        return new LocaleMessage(langBuilderMap);
    }

    /**
     * 构建信息（仅有行号）
     *
     * @param messageEnum
     * @return
     */
    public LocaleMessage buildOnlyRow(ExcelMessageEnum messageEnum) {
        String messageCode = messageEnum.getMessageCode();
        Object[] args = messageEnum.getArgs();
        Map<String, StringBuilder> langBuilderMap = new HashMap<>();
        for (Locale locale : supportLang) {
            StringBuilder localeMsg = new StringBuilder(msgPrefix).append(MessageUtils.message(locale, ExcelMessageEnum.MESSAGE_HEAD_NO_COLUMN.getMessageCode(), nowShowRow))
                                                                  .append(MessageUtils.message(locale, messageCode, args))
                                                                  .append(msgSuffix);
            langBuilderMap.put(locale.toString(), localeMsg);
        }
        return new LocaleMessage(langBuilderMap);
    }

    public LocaleMessage build(ZFunction<T, ?> function, ExcelMessageEnum messageEnum) {
        return build(function, messageEnum, this.nowShowRow);
    }

    public LocaleMessage build(ZFunction<T, ?> function, ExcelMessageEnum messageEnum, Integer showRowIndex) {
        String fieldName = getFieldName(function);
        String titleName = titleMap.get(fieldName);
        String messageCode = messageEnum.getMessageCode();
        Object[] args = messageEnum.getArgs();

        Map<String, StringBuilder> langBuilderMap = new HashMap<>();
        for (Locale locale : supportLang) {
            StringBuilder localeMsg;
            if (msgPrefix != null) {
                localeMsg = new StringBuilder(msgPrefix);
            } else {
                localeMsg = new StringBuilder();
            }
            localeMsg.append(MessageUtils.message(locale, ExcelMessageEnum.MESSAGE_HEAD.getMessageCode(), showRowIndex, titleName));
            localeMsg.append(MessageUtils.message(locale, messageCode, args));

            if (msgSuffix != null) {
                localeMsg.append(msgSuffix);
            }
            langBuilderMap.put(locale.toString(), localeMsg);
        }
        return new LocaleMessage(langBuilderMap);
    }

    private String getFieldName(ZFunction<T, ?> function) {
        SerializedLambda serializedLambda = getSerializedLambda(function);
        String implMethodName = serializedLambda.getImplMethodName();
        String prefix = null;
        if (implMethodName.startsWith("get")) {
            prefix = "get";
        } else if (implMethodName.startsWith("is")) {
            prefix = "is";
        }

        if (prefix == null) {
            log.error("未找到对应的get或is方法");
        }
        String fieldName = implMethodName.replace(prefix, "");

        char firstChar = fieldName.charAt(0);
        if (Character.isUpperCase(firstChar)) {
            fieldName = Character.toLowerCase(firstChar) + fieldName.substring(1);
        }
        return fieldName;
    }

    /**
     * 获取Lambda方法的方法名
     */
    private static SerializedLambda getSerializedLambda(Serializable fn) {
        SerializedLambda lambda = CLASS_LAMDBA_CACHE.get(fn.getClass());
        if (lambda == null) {
            try {
                Method method = fn.getClass().getDeclaredMethod("writeReplace");
                method.setAccessible(Boolean.TRUE);
                lambda = (SerializedLambda) method.invoke(fn);
                CLASS_LAMDBA_CACHE.put(fn.getClass(), lambda);
            } catch (Exception e) {

            }
        }
        return lambda;
    }

}
