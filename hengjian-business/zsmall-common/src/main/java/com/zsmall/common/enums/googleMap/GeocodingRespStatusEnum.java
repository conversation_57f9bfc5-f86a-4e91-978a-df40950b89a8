package com.zsmall.common.enums.googleMap;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/6/17
 **/
@Getter
public enum GeocodingRespStatusEnum {

  /**
   * 请求成功
   */
  OK("Request Success."),
  /**
   * indicates that the geocode was successful but returned no results. This may occur if the geocoder was passed a non-existent address
   * 指示地理代码成功，但没有返回结果。如果传递给地理编码器一个不存在的地址，就可能发生这种情况
   */
  ZERO_RESULTS("The address is invalid."),
  /**
   * The API key is missing or invalid.   Key失效
   * Billing has not been enabled on your account.  账单尚未在您的帐户上启用。
   * A self-imposed usage cap has been exceeded.  已超出自行设定的使用上限。
   * The provided method of payment is no longer valid (for example, a credit card has expired).  提供的付款方式不再有效（例如，信用卡已过期）
   */
  OVER_QUERY_LIMIT("The API key is missing or invalid. Please contact the system administrator."),
  /**
   * indicates that your request was denied.
   * 请求被拒绝
   */
  REQUEST_DENIED("indicates that your request was denied."),
  /**
   * generally indicates that the query (address, components or latlng) is missing.
   *  通常表示查询(地址、组件或经纬度)缺失。
   */
  INVALID_REQUEST("generally indicates that the query (address, components or latlng) is missing."),
  /**
   * indicates that the request could not be processed due to a server error. The request may succeed if you try again.
   * 表示由于服务器错误而无法处理请求。如果您再次尝试，请求可能会成功。
   */
  UNKNOWN_ERROR("indicates that the request could not be processed due to a server error. The request may succeed if you try again.")
  ;

  private String value;

  GeocodingRespStatusEnum(String value) {
    this.value = value;
  }

  public static GeocodingRespStatusEnum fromName(String name) {
    name = name.toUpperCase();
    for (GeocodingRespStatusEnum state : GeocodingRespStatusEnum.values()) {
      if (Objects.equals(name, state.name())) {
        return state;
      }
    }
    return null;
  }
}
