package com.zsmall.order.entity.domain.vo.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.zsmall.order.entity.domain.vo.OrderDetailsAttachmentVo;
import lombok.Data;

import java.util.List;

/**
 * 响应信息-返回订单详情
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class OrderDetailVo {

    /**
     * 子订单列表
     */
    private List<OrderItemDetailVo> orderItemsCategory;

    /**
     * 是否可以上传快递标签
     */
    private Boolean canUploadLabel = false;
    /**
     * 是否存在可以下载的快递标签
     */
    private Boolean hasLabel = false;

    /**
     * 钱包是否被冻结
     */
    private boolean walletFreeze = false;
//    /**
//     * 快递标签文件名
//     */
//    private String labelFileName;
//    /**
//     * 订单标签
//     */
//    private String shippingLabelShowUrl;
//
//    @ApiModelProperty(value = "订单附件展示Url")
//    @Schema(title = "订单附件展示Url")
//    private String attachmentShowUrl;
//
//    @ApiModelProperty(value = "订单附件名")
//    @Schema(title = "订单附件名")
//    private String attachmentName;

    /**
     * 订单信息
     */
    private OrderPageVo orderBody;
    /**
     * 默认承运商
     */
    private String defaultCarrier;
    /**
     * 默认履约类型
     */
    private String defaultFulfillmentType;
    /**
     * 附件信息
     */
    private List<OrderDetailsAttachmentVo> orderDetailsAttachmentVos;

}
