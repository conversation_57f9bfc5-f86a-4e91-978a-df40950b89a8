package com.zsmall.order.entity.domain.vo.wholesaleOrder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应体-批发订单详情
 * <AUTHOR>
 * @date 2023/2/24
 */
@Data
@NoArgsConstructor
@Schema(name = "响应体-批发订单详情")
public class WholesaleOrderVo {

  @Schema(title  = "批发订单编号")
  private String wiOrderNo;

  @Schema(title  = "订购时间")
  private String orderDateTime;

  @Schema(title  = "订单状态（code）")
  private Integer orderStatus;

  @Schema(title  = "订单状态（中文）")
  private String orderStatus_zh_CN;

  @Schema(title  = "订单状态（英文）")
  private String orderStatus_en_US;

  @Schema(title  = "订单阶段：10-供货商未录入价格，此时供货商侧应显示“录入费用”按钮，20-供货商已录入价格，此时分销商侧应显示“预约提货”按钮")
  private Integer orderStage;

  @Schema(title  = "支持的发货方式")
  private List<String> supportDeliveryTypes;

  @Schema(title  = "商品集合")
  private List<WholesaleOrderProductVo> productList;

  @Schema(title  = "订购信息")
  private WholesaleOrderInfoVo orderInfo;

  @Schema(title  = "订单金额信息")
  private WholesaleOrderAmountVo amountInfo;

  @Schema(title  = "收货地址信息")
  private WholesaleOrderAddressVo addressInfo;

  @Schema(title  = "仓库信息")
  private WholesaleOrderWarehouseVo warehouseInfo;

}
