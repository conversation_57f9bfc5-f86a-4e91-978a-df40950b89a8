package com.zsmall.order.entity.anno.aspect;

import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.order.entity.anno.annotaion.PayLimit;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.redisson.api.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/8 11:05
 */
@Aspect
@Component
public class PayLimitAspect {

    @Resource
    private RedissonClient redissonClient;
    @Before("@annotation(payLimit)")
    public void before(JoinPoint joinPoint, PayLimit payLimit) throws NoSuchAlgorithmException {
        // 锁tenantId+method 限流 value 是入参 // 用不了bitmap 用list吧

        int interval = payLimit.interval();
        TimeUnit timeUnit = payLimit.timeUnit();

        Object[] args = joinPoint.getArgs();
        String methodName = joinPoint.getSignature().getName();
        for (Object arg : args) {
            if(arg instanceof OrderPayBo){
                LoginUser loginUser = LoginHelper.getLoginUser();
                String tenantId = loginUser.getTenantId();
//                String tenantId = "test-01";
                List<String> orderNoList = ((OrderPayBo) arg).getOrderNoList();
                // 将orderNoList转为json字符串存在redis的set数据结构中
                String key = "payLimit:" + methodName+":"+tenantId;
                for (String order : orderNoList) {
                    boolean orderNumberExists = isOrderNumberExistsV2(order, key);
                    if (orderNumberExists) {
                        throw new RuntimeException("订单号"+order+"已在支付中");
                    }else{
                        addOrderNumberV2(key, order,interval,timeUnit);
                    }
                }

            }
        }
    }

    /**
     * 功能描述：添加订单号
     *
     * @param key         钥匙
     * @param orderNumber 订单号
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/11
     */
    public boolean addOrderNumber(String key, String orderNumber,long timeToLive, TimeUnit timeUnit) {

        // 获取订单号集合的RSet实例
        RSet<String> orderNumberSet = redissonClient.getSet(key);

        // 尝试添加订单号到集合中
        // SADD命令在元素已存在时不会添加，并返回0；添加新元素时返回1
        boolean result = orderNumberSet.add(orderNumber);

        redissonClient.getBucket(key).expire(timeToLive, timeUnit);

        // 根据SADD命令的返回值判断订单号是否已存在
        return result ; // 如果result大于0，说明是新添加的订单号
    }

    public boolean addOrderNumberV2(String key, String orderNumber,long timeToLive, TimeUnit timeUnit) {
        String orderNumberKey = key + ":" + orderNumber;
        // 获取订单号集合的RSet实例
        RBucket<String> bucket = redissonClient.getBucket(orderNumberKey);
        bucket.set(orderNumber);
        bucket.expire(timeToLive, timeUnit);
        // 根据SADD命令的返回值判断订单号是否已存在
        return true ; // 如果result大于0，说明是新添加的订单号
    }

    /**
     * 功能描述：订单号是否存在
     *
     * @param orderNumber 订单号
     * @param key         钥匙
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/11
     */
    public boolean isOrderNumberExists(String orderNumber, String key) {

        // 获取订单号集合的RSet实例
        RSet<String> orderNumberSet = redissonClient.getSet(key);

        // SISMEMBER命令检查订单号是否存在于集合中
        return orderNumberSet.contains(orderNumber);
    }
    /**
     * 功能描述：订单号是否存在
     *
     * @param orderNumber 订单号
     * @param key         钥匙
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/11
     */
    public boolean isOrderNumberExistsV2(String orderNumber, String key) {
        String orderNumberKey = key + ":" + orderNumber;
        RBucket<String> bucket = redissonClient.getBucket(orderNumberKey);
        long l = bucket.remainTimeToLive();
        // 如果TTL小于0，表示键已经过期或不存在
        return l != -2;
    }
}
