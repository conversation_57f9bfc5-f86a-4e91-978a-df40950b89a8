package com.zsmall.order.entity.domain.bo.refund;

import com.zsmall.common.domain.bo.TrackingNoBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求信息-退货物流信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SupplementRefundInfoBo {

    /**
     * 退款单编号
     */
    private String orderRefundNo;

    /**
     * 退款子单编号
     */
    private String orderRefundItemNo;

    /**
     * 退货方式：自选物流公司寄回，安排物流寄回
     */
    private String returnType;

    /**
     * 退货取件地址
     */
    private String pickupAddress;
    /**
     * 退货物流承运商（type为Myself时需要）
     */
    private String carrier;
    /**
     * 退货物流单号（type为Myself时需要）
     */
    private String trackingNo;
    /**
     * 物流跟踪单号集合
     */
    private List<TrackingNoBo> trackingInfoList;
}
