package com.zsmall.order.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.order.entity.domain.OrderRefundRule;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 售后规则视图对象 order_refund_rule
 *
 * <AUTHOR> Li
 * @date 2023-06-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrderRefundRule.class)
public class OrderRefundRuleVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 退款规则编号
     */
    @ExcelProperty(value = "退款规则编号")
    private String refundRuleNo;

    /**
     * 适用履约状态（适用于未发货、已发货等状态）
     */
    @ExcelProperty(value = "适用履约状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "适=用于未发货、已发货等状态")
    private String applicableFulfillment;

    /**
     * 售后原因明细（中文）
     */
    @ExcelProperty(value = "售后原因明细", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "中=文")
    private String refundReasonZhCn;

    /**
     * 售后原因明细（英文）
     */
    @ExcelProperty(value = "售后原因明细", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "英=文")
    private String refundReasonEnUs;

    /**
     * 是否需要员工审核（10-不需要，20-需要，30-超阈值时需要）
     */
    @ExcelProperty(value = "是否需要员工审核", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=0-不需要，20-需要，30-超阈值时需要")
    private Long whetherReviewMd;

    /**
     * 是否支持修改退款金额（10-不支持，20-支持）
     */
    @ExcelProperty(value = "是否支持修改退款金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=0-不支持，20-支持")
    private Long whetherSupportModifyAmount;

    /**
     * 是否需要提供图片举证（10-不需要，20-需要）
     */
    @ExcelProperty(value = "是否需要提供图片举证", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=0-不需要，20-需要")
    private Long whetherProvidePictures;

    /**
     * 是否还原库存（10-不还原，20-整单还原，30-可执行金额为零时还原）
     */
    @ExcelProperty(value = "是否还原库存", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=0-不还原，20-整单还原，30-可执行金额为零时还原")
    private Long whetherRestoreInventory;

    /**
     * 排序（业务中按照倒序查询）
     */
    @ExcelProperty(value = "排序", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "业=务中按照倒序查询")
    private Long sort;


}
