package com.zsmall.order.entity.domain.bo.orderImport;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/10/10 16:03
 */
@Data
public class TempOrderUpdateV2Bo {
    /**
     * 临时订单号（都需要）
     */
    private String tempOrderNo;

    /**
     * 收件人名称
     */
    private String recipientName;

    /**
     * 地址1
     */
    private String address1;
    /**
     * 地址2
     */
    private String address2;
    /**
     * 地址3
     */
    private String address3;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 城市
     */
    private String city;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 邮编
     */
    private String zipCode;
    /**
     * 国家
     */
    private Long countryId;
    /**
     * 国家
     */
    private String country;
    /**
     * 州/省/地区
     */
    private Long stateId;
    /**
     * 州/省/地区
     */
    private String state;
    /**
     * 是否选择第三方物流
     */
    private Boolean logisticsThirdBilling;
    /**
     * 第三方物流商（UPS，FedEx）
     */
    private String logisticsCarrier;
    /**
     * 物流服务名称
     */
    private String logisticsServiceName;
    /**
     * 第三方物流发货商账号
     */
    private String logisticsAccount;
    /**
     * 第三方发货商账号邮编
     */
    private String logisticsAccountZipCode;
    /**
     * 物流类型（PickUp-自提，DropShipping-代发商品）
     */
    private String logisticsType;
    /**
     * 仓库编号
     */
    private String warehouseCode;
    /**
     * 仓库系统唯一编号
     */
    private String warehouseSystemCode;
    /**
     * 物流跟踪单号集合
     */
    private List<String> logisticsTrackingNo;
    /**
     * ItemNo
     */
    private String productSkuCode;
    /**
     * 购物车订单
     */
    private Boolean isMarketplace;
    /**
     * 渠道订单号
     */
    private String storeOrderId;
    /**
     * 渠道商店名称
     */
    private String storeName;

    private String channelType;

    /**
     * 文件（上传快递标签、上传订单附件需要）
     */
    private MultipartFile multipartFile;
}
