package com.zsmall.order.biz.job.payment;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.domain.vo.XxlJobPaymentVO;
import com.zsmall.system.biz.support.PaymentExtensionSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/18 17:16
 */
@Slf4j
@Service
public class PaymentHandlerJob {
    @Resource
    private PaymentExtensionSupport paymentExtensionSupport;

    /**
     * 功能描述：更新消息推送异常的单据
     *
     * <AUTHOR>
     * @date 2024/09/18
     */
    @XxlJob("updatePaymentReceiptAndSendMsg")
    public void updatePaymentReceiptAndSendMsg() {
        String jobParam = XxlJobHelper.getJobParam();
        Date afterThat = JSON.parseObject(jobParam, XxlJobPaymentVO.class).getDate();
        paymentExtensionSupport.updatePaymentReceiptAndSendMsg(afterThat);
    }
}
