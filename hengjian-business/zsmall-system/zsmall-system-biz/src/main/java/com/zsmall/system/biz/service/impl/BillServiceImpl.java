package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.excel.convert.ExcelBigNumberConvert;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSObtainEvent;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.mapper.SysTenantMapper;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.bill.*;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.mapper.OrderItemMapper;
import com.zsmall.order.entity.mapper.OrderItemShippingRecordMapper;
import com.zsmall.system.biz.service.BillService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.convert.BillConvert;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.bo.BillAdminExport;
import com.zsmall.system.entity.domain.bo.BillAdminRelationDetailDto;
import com.zsmall.system.entity.domain.bo.BillExport;
import com.zsmall.system.entity.domain.bo.BillRelationDetailDto;
import com.zsmall.system.entity.domain.bo.bill.*;
import com.zsmall.system.entity.domain.vo.bill.*;
import com.zsmall.system.entity.domain.vo.bill.billclass.BillClassDetailVo;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.mapper.BillMapper;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 账单相关-业务实现
 *
 * <AUTHOR>
 * @date 2023/7/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillServiceImpl implements BillService {

    // private static final DateTimeFormatter dateTimeFormatter =
    //     DateTimeFormatter.ofPattern("MMM d, yyyy hh:mm:ss a", Locale.ENGLISH);
    private static final DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);

    private final BillSupport billSupport;

    private final IBillService iBillService;
    private final IBillAbstractService iBillAbstractService;
    private final IBillRelationService iBillRelationService;
    private final IBillErrorRecordService iBillErrorRecordService;
    private final IBillLogService iBillLogService;
    private final IBillAbstractLogService iBillAbstractLogService;
    private final IBillAbstractDetailLogService iBillAbstractDetailLogService;
    private final IBillRelationLogService iBillRelationLogService;
    private final IBillRelationDetailLogService iBillRelationDetailLogService;
    private final IBillAbstractDetailService iBillAbstractDetailService;
    private final IBillRelationDetailService iBillRelationDetailService;

    private final IDownloadRecordService iDownloadRecordService;

    private final FileProperties fileProperties;
    //order_item_shipping_record
    private final OrderItemShippingRecordMapper orderItemShippingRecordMapper;
    private final SysTenantMapper sysTenantMapper;
    private final OrderItemMapper orderItemMapper;
    /**
     * 初次生成账单
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> firstGenerateBill(GenerateBillBo bo) {

        LoginHelper.getLoginUser(TenantType.Manager);

        List<String> tenantIdList = bo.getTenantIdList();

        List<SysTenantVo> tenantList = SystemEventUtils.queryTenantsByTenantType(TenantType.Supplier);

        // 获取最近一次账单结算时间
        LocalDateTime latestSettlementDate = billSupport.getLatestSettlementDate();
        // 账单周期编号（不唯一，一般为结算时间的yyyyMMdd）
        String billCycleNo = LocalDateTimeUtil.format(latestSettlementDate, "yyyyMMdd");
        log.info("最近一次的账单周期编号 = {}", billCycleNo);

        LocalDateTime latestSettlementCycleBegin = billSupport.getLatestSettlementCycleBegin();
        LocalDateTime latestSettlementCycleEnd = billSupport.getLatestSettlementCycleEnd();

        List<String> duplicateBillNo = new ArrayList<>();
        List<Bill> billList = new ArrayList<>();
        if (CollUtil.isNotEmpty(tenantList)) {
            for (SysTenantVo tenantVo : tenantList) {
                String tenantId = tenantVo.getTenantId();

                Bill query = new Bill();
                query.setTenantId(tenantId);
                query.setBillCycleNo(billCycleNo);

                long count = iBillService.countByEntityNotTenant(query);
                log.info("存在数量 = {}", count);
                if (count <= 0) {
                    String billNo = billSupport.generateBillNo(billCycleNo);
                    // 防止短时间生成太多导致重复
                    while (duplicateBillNo.contains(billNo)) {
                        billNo = billSupport.generateBillNo(billCycleNo);
                    }
                    duplicateBillNo.add(billNo);

                    Bill newBill = new Bill();
                    newBill.setTenantId(tenantId);
                    newBill.setBillNo(billNo);
                    newBill.setBillCycleNo(billCycleNo);
                    newBill.setSettlementDateTime(latestSettlementDate);
                    newBill.setSettlementCycleBegin(latestSettlementCycleBegin);
                    newBill.setSettlementCycleEnd(latestSettlementCycleEnd);
                    newBill.setCurrentIncome(BigDecimal.ZERO);
                    newBill.setCurrentExpenditure(BigDecimal.ZERO);
                    newBill.setCurrentCircularDeposit(BigDecimal.ZERO);
                    newBill.setPreviousCircularDeposit(BigDecimal.ZERO);
                    newBill.setCircularDepositRatio(BigDecimal.valueOf(0.2));
                    newBill.setCurrentTotalAmount(BigDecimal.ZERO);
                    newBill.setBillState(BillStateEnum.Unsettled);
                    newBill.setGenerateState(GenerateStateEnum.NotGenerated);
                    newBill.setWithdrawalState(WithdrawalStateEnum.NotWithdrawal);
                    billList.add(newBill);
                }
            }
        }

        if (CollUtil.isNotEmpty(billList)) {
            TenantHelper.ignore(() -> iBillService.saveBatch(billList));

            List<BillAbstract> billAbstractList = new ArrayList<>();
            // 生成收入和支出的摘要主表
            for (Bill bill : billList) {
                BillAbstract billAbstract_income = new BillAbstract();
                billAbstract_income.setBillId(bill.getId());
                billAbstract_income.setAbstractType(AbstractTypeEnum.Income);
                billAbstract_income.setAbstractTotalAmount(BigDecimal.ZERO);
                billAbstractList.add(billAbstract_income);

                BillAbstract billAbstract_expenditure = new BillAbstract();
                billAbstract_expenditure.setBillId(bill.getId());
                billAbstract_expenditure.setAbstractType(AbstractTypeEnum.Expenditure);
                billAbstract_expenditure.setAbstractTotalAmount(BigDecimal.ZERO);
                billAbstractList.add(billAbstract_expenditure);
            }
            iBillAbstractService.saveBatch(billAbstractList);
        }
        return R.ok();
    }

    /**
     * 分页查询账单列表
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public BillPageVo queryBillPage(BillListBo bo, PageQuery pageQuery) {

        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager, TenantType.Supplier);
        String tenantType = loginUser.getTenantType();
        String tenantId = loginUser.getTenantId();
        String billDate = bo.getBillDate();
        final LocalDateTime reqLocalDateTimeBegin;
        final LocalDateTime reqLocalDateTimeEnd;
        if (StrUtil.isNotBlank(billDate)) {
            DateTime reqDate = DateUtil.parse(billDate, "yyyy-MM");
            reqLocalDateTimeBegin = DateUtil.toLocalDateTime(DateUtil.beginOfMonth(reqDate)).withNano(0);
            reqLocalDateTimeEnd = DateUtil.toLocalDateTime(DateUtil.endOfMonth(reqDate)).withNano(0);
        } else {
            reqLocalDateTimeBegin = null;
            reqLocalDateTimeEnd = null;
        }


        Page<Bill> billPage;
        final String queryType;
        final String queryValue;
        if (TenantType.Manager.name().equals(tenantType)) {
            queryType = bo.getQueryType();
            queryValue = bo.getQueryValue();
            billPage = iBillService.queryPage(queryType, queryValue, reqLocalDateTimeBegin, reqLocalDateTimeEnd, pageQuery.build());
        } else {
            queryType = "UserCode";
            queryValue = tenantId;
            billPage = iBillService.queryPage(null, null, reqLocalDateTimeBegin, reqLocalDateTimeEnd, pageQuery.build());
        }

        List<Bill> billList = billPage.getRecords();
        List<BillListVo> results = new ArrayList<>();

        for (Bill bill : billList) {
            String billNo = bill.getBillNo();
            LocalDateTime settlementCycleBegin = bill.getSettlementCycleBegin();
            LocalDateTime settlementCycleEnd = bill.getSettlementCycleEnd();
            BigDecimal currentIncome = bill.getCurrentIncome();
            BigDecimal currentExpenditure = bill.getCurrentExpenditure();
            BigDecimal previousCircularDeposit = bill.getPreviousCircularDeposit();
            BigDecimal currentCircularDeposit = bill.getCurrentCircularDeposit();
            BigDecimal currentTotalAmount = bill.getCurrentTotalAmount();
            BillStateEnum billState = bill.getBillState();

            BillListVo respBillListBody = new BillListVo();
            String settlementCycleBegin_Str = LocalDateTimeUtil.format(settlementCycleBegin, dateTimeFormatter);
            String settlementCycleEnd_Str = LocalDateTimeUtil.format(settlementCycleEnd, dateTimeFormatter);

            String currentIncome_Str = "+$" + DecimalUtil.bigDecimalToString(currentIncome);
            String currentExpenditure_Str = "-$" + DecimalUtil.bigDecimalToString(currentExpenditure);
            String previousCircularDeposit_Str = "+$" + DecimalUtil.bigDecimalToString(previousCircularDeposit);
            String currentCircularDeposit_Str = "-$" + DecimalUtil.bigDecimalToString(currentCircularDeposit);
            String currentTotalAmount_Str = "$" + DecimalUtil.bigDecimalToString(currentTotalAmount);

            respBillListBody.setBillNo(billNo);
            respBillListBody.setSettlementCycleBegin(settlementCycleBegin_Str);
            respBillListBody.setSettlementCycleEnd(settlementCycleEnd_Str);
            respBillListBody.setCurrentIncome(currentIncome_Str);
            respBillListBody.setCurrentExpenditure(currentExpenditure_Str);
            respBillListBody.setPreviousCircularDeposit(previousCircularDeposit_Str);
            respBillListBody.setCurrentCircularDeposit(currentCircularDeposit_Str);
            respBillListBody.setCurrentTotalAmount(currentTotalAmount_Str);
            respBillListBody.setBillState(billState.getCode());

            if (TenantType.Manager.name().equals(tenantType)) {
                respBillListBody.setTenantId(bill.getTenantId());
            }

            results.add(respBillListBody);
        }

        String canWithdrawnTotalAmount = null;
        String circularDepositTotalAmount = null;
        if (TenantType.Manager.name().equals(tenantType)) {

            // 查询用户时，循环保证金和可提现金额为该供货商的
            if (StrUtil.equals(queryType, "UserCode") && StrUtil.isNotBlank(queryValue)) {
                BigDecimal unsettledTotalAmount = TenantHelper.ignore(() -> iBillService.sumUnsettledTotalAmount(queryValue));
                canWithdrawnTotalAmount = DecimalUtil.bigDecimalToString(unsettledTotalAmount);

                BigDecimal sumCircularDeposit = TenantHelper.ignore(() -> iBillService.sumCircularDeposit(queryValue));
                circularDepositTotalAmount = DecimalUtil.bigDecimalToString(sumCircularDeposit);
            } else {
                // 不查询用户时，循环保证金和可提现金额为平台所有供货商的
                BigDecimal unsettledTotalAmount = TenantHelper.ignore(() -> iBillService.sumUnsettledTotalAmount(null));
                canWithdrawnTotalAmount = DecimalUtil.bigDecimalToString(unsettledTotalAmount);

                BigDecimal sumCircularDeposit = TenantHelper.ignore(() -> iBillService.sumCircularDeposit(null));
                circularDepositTotalAmount = DecimalUtil.bigDecimalToString(sumCircularDeposit);
            }

        }

        TableDataInfo<BillListVo> tableDataInfo = TableDataInfo.build(results, billPage.getTotal());
        BillPageVo vo = BeanUtil.toBean(tableDataInfo, BillPageVo.class);
        vo.setCanWithdrawnTotalAmount(canWithdrawnTotalAmount);
        vo.setCircularDepositTotalAmount(circularDepositTotalAmount);

        return vo;
    }

    /**
     * 获取供应商可结余账单列表
     */
    @Override
    public R<BillVo> queryBalanceBillList() {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = loginUser.getTenantId();

        List<Bill> billEntities = iBillService.queryBalanceBillList(tenantId);
        List<BillListVo> results = new ArrayList<>();
        BigDecimal withdrawableAmount = BigDecimal.ZERO;
        for (Bill bill : billEntities) {
            String billNo = bill.getBillNo();
            LocalDateTime settlementCycleBegin = bill.getSettlementCycleBegin();
            LocalDateTime settlementCycleEnd = bill.getSettlementCycleEnd();
            BigDecimal currentTotalAmount = bill.getCurrentTotalAmount();
            //账单金额为0，则过滤掉
            if (NumberUtil.equals(BigDecimal.ZERO, currentTotalAmount)) {
                continue;
            }

            withdrawableAmount = NumberUtil.add(currentTotalAmount, withdrawableAmount);
            BillListVo respBillListBody = new BillListVo();
            String settlementCycleBegin_Str = LocalDateTimeUtil.format(settlementCycleBegin, dateTimeFormatter);
            String settlementCycleEnd_Str = LocalDateTimeUtil.format(settlementCycleEnd, dateTimeFormatter);

            String currentTotalAmount_Str = "$" + DecimalUtil.bigDecimalToString(currentTotalAmount);

            respBillListBody.setBillNo(billNo);
            respBillListBody.setSettlementCycle(settlementCycleBegin_Str + "至" + settlementCycleEnd_Str);
            respBillListBody.setSettlementCycle_en_US(settlementCycleBegin_Str + " to " + settlementCycleEnd_Str);
            respBillListBody.setSettlementCycle_zh_CN(settlementCycleBegin_Str + "至" + settlementCycleEnd_Str);
            respBillListBody.setCurrentTotalAmount(currentTotalAmount_Str);
            respBillListBody.setCurrentTotalAmountNum(currentTotalAmount);
            results.add(respBillListBody);
        }

        BillVo billVo = new BillVo();
        billVo.setBillListBody(results);
        billVo.setBalanceStr(DecimalUtil.bigDecimalToString(withdrawableAmount));
        billVo.setBalance(withdrawableAmount);
        return R.ok(billVo);
    }

    /**
     * 查询账单详情
     *
     * @param bo
     */
    @Override
    public R<BillDetailVo> queryBillDetail(BillBaseBo bo) {

        String billNo = bo.getBillNo();
        if (StrUtil.isBlank(billNo)) {
            return R.fail(ZSMallStatusCodeEnum.MISSING_VALUE_IN_THE_INTERFACE);
        }

        LoginHelper.getLoginUser(TenantType.Manager, TenantType.Supplier);

        Bill queryEntity = new Bill();
        queryEntity.setBillNo(billNo);
        List<Bill> billList = iBillService.queryByEntity(queryEntity);
        if (CollUtil.isNotEmpty(billList)) {
            Bill bill = billList.get(0);

            Long previousBillId = bill.getPreviousBillId();
            // 上期账单
            Bill previousBill = TenantHelper.ignore(() -> iBillService.getById(previousBillId), TenantType.Manager);

            billNo = bill.getBillNo();
            LocalDateTime settlementCycleBegin = bill.getSettlementCycleBegin();
            LocalDateTime settlementCycleEnd = bill.getSettlementCycleEnd();
            BigDecimal currentIncome = bill.getCurrentIncome();
            BigDecimal currentExpenditure = bill.getCurrentExpenditure();
            BigDecimal previousCircularDeposit = bill.getPreviousCircularDeposit();
            BigDecimal currentCircularDeposit = bill.getCurrentCircularDeposit();
            BigDecimal currentTotalAmount = bill.getCurrentTotalAmount();
            Integer billState = bill.getBillState().getValue();

            BigDecimal circularDepositRatio = bill.getCircularDepositRatio();

            String settlementCycleBegin_Str = LocalDateTimeUtil.format(settlementCycleBegin, dateTimeFormatter);
            String settlementCycleEnd_Str = LocalDateTimeUtil.format(settlementCycleEnd, dateTimeFormatter);

            String currentIncome_Str = DecimalUtil.bigDecimalToString(currentIncome);
            String currentExpenditure_Str = DecimalUtil.bigDecimalToString(currentExpenditure);
            String previousCircularDeposit_Str = DecimalUtil.bigDecimalToString(previousCircularDeposit);
            String currentCircularDeposit_Str = DecimalUtil.bigDecimalToString(currentCircularDeposit);
            String currentTotalAmount_Str = DecimalUtil.bigDecimalToString(currentTotalAmount);

            Long billId = bill.getId();

            // 查询摘要
            List<BillAbstract> abstractList = iBillAbstractService.queryByBillId(billId);
            List<BillAbstractVo> billAbstractBodyList = new ArrayList<>();
            for (BillAbstract billAbstractEntity : abstractList) {
                Long billAbstractId = billAbstractEntity.getId();
                AbstractTypeEnum abstractType = billAbstractEntity.getAbstractType();
                BillAbstractVo billAbstractVo = new BillAbstractVo();

                billAbstractVo.setAbstractType(abstractType.name());

                // 摘要字段详情
                List<BillAbstractDetail> abstractDetailList =
                    iBillAbstractDetailService.queryByBillAbstractId(billAbstractId);
                if (CollUtil.isNotEmpty(abstractDetailList)) {
                    for (BillAbstractDetail billAbstractDetailEntity : abstractDetailList) {
                        AbstractFieldTypeEnum fieldType = billAbstractDetailEntity.getFieldType();
                        BigDecimal fieldValue = billAbstractDetailEntity.getFieldValue();
                        billAbstractVo.addDetail(fieldType, DecimalUtil.bigDecimalToString(fieldValue));
                    }
                }
                billAbstractBodyList.add(billAbstractVo);
            }

            // 本期循环保证金摘要
            BillAbstractVo currentCircularDepositAbstract = new BillAbstractVo();
            currentCircularDepositAbstract.setAbstractType(AbstractTypeEnum.CurrentCircularDeposit.name());
            // 本期实际收入 = 本期收入 - 本期支出
            BigDecimal actualCurrentIncome = currentIncome.subtract(currentExpenditure);
            currentCircularDepositAbstract.setCircularDepositValue(DecimalUtil.bigDecimalToString(actualCurrentIncome));
            // 转换循环保证金的比例展示
            BigDecimal ratio_100 = NumberUtil.mul(circularDepositRatio, 100);
            currentCircularDepositAbstract.setCircularDepositRatio(NumberUtil.toStr(ratio_100) + "%");
            billAbstractBodyList.add(currentCircularDepositAbstract);

            // 上期循环保证金摘要
            BillAbstractVo previousCircularDepositAbstract = new BillAbstractVo();
            previousCircularDepositAbstract.setAbstractType(AbstractTypeEnum.PreviousCircularDeposit.name());
            if (previousBill != null) {
                BigDecimal previousCircularDepositRatio = previousBill.getCircularDepositRatio();

                // 上期收入
                BigDecimal previousCurrentIncome = previousBill.getCurrentIncome();
                // 上期支出
                BigDecimal previousCurrentExpenditure = previousBill.getCurrentExpenditure();
                BigDecimal previousActualCurrentIncome = previousCurrentIncome.subtract(previousCurrentExpenditure);

                previousCircularDepositAbstract
                    .setCircularDepositValue(DecimalUtil.bigDecimalToString(previousActualCurrentIncome));
                previousCircularDepositAbstract.setCircularDepositRatio(NumberUtil.toStr(ratio_100) + "%");
            } else {
                previousCircularDepositAbstract.setCircularDepositValue("0.00");
                previousCircularDepositAbstract.setCircularDepositRatio("0%");
            }
            billAbstractBodyList.add(previousCircularDepositAbstract);

            BillDetailVo billDetailVo = new BillDetailVo();
            billDetailVo.setBillAbstractList(billAbstractBodyList);

            billDetailVo.setBillNo(billNo);
            billDetailVo.setSettlementCycleBegin(settlementCycleBegin_Str);
            billDetailVo.setSettlementCycleEnd(settlementCycleEnd_Str);
            billDetailVo.setCurrentIncome(currentIncome_Str);
            billDetailVo.setCurrentExpenditure(currentExpenditure_Str);
            billDetailVo.setPreviousCircularDeposit(previousCircularDeposit_Str);
            billDetailVo.setCurrentCircularDeposit(currentCircularDeposit_Str);
            billDetailVo.setCurrentTotalAmount(currentTotalAmount_Str);
            billDetailVo.setBillState(billState);

            return R.ok(billDetailVo);
        } else {
            return R.fail(ZSMallStatusCodeEnum.BILL_NOT_FOUND);
        }
    }

    /**
     * 查询账单分类列表
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public R<BillClassVo> queryBillClassList(BillBaseBo bo, PageQuery pageQuery) {

        String billNo = bo.getBillNo();
        if (StrUtil.isBlank(billNo)) {
            return R.fail(ZSMallStatusCodeEnum.MISSING_VALUE_IN_THE_INTERFACE);
        }

        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager, TenantType.Supplier);
        String tenantId = loginUser.getTenantId();
        String tenantType = loginUser.getTenantType();
        Bill queryEntity = new Bill();
        if (TenantType.Supplier.name().equals(tenantType)) {
            queryEntity.setTenantId(tenantId);
        }
        queryEntity.setBillNo(billNo);

        List<Bill> billList = iBillService.queryByEntity(queryEntity);

        Map<String, BillRelationClassVo> income_classNameBodyMap = new HashMap<>();
        Map<String, BillRelationClassVo> expenditure_classNameBodyMap = new HashMap<>();
        if (CollUtil.isNotEmpty(billList)) {
            Bill bill = billList.get(0);
            Long billId = bill.getId();

            List<String> relationTypeList = iBillRelationService.queryRelationTypeListByBillId(billId);
            if (CollUtil.isNotEmpty(relationTypeList)) {
                for (String relationType : relationTypeList) {
                    Page<BillRelation> resultPage = iBillRelationService.queryByBillIdAndRelationType(pageQuery.build(), billId, relationType);
                    long pages = resultPage.getPages();
                    long total = resultPage.getTotal();

                    List<BillRelation> relationList = resultPage.getRecords();

                    BigDecimal totalAmount = iBillRelationService
                        .queryRelationTypeTotalAmount(billId, relationType, RelationFieldTypeEnum.TotalAmount);

                    for (BillRelation billRelation : relationList) {
                        RelationTypeEnum relationTypeEnum = RelationTypeEnum.valueOf(relationType);

                        AbstractTypeEnum belongAbstractType = billRelation.getBelongAbstractType();
                        if (AbstractTypeEnum.Income.equals(belongAbstractType)) {
                            BillRelationClassVo respBillRelationClassBody = income_classNameBodyMap.get(relationType);
                            if (respBillRelationClassBody == null) {
                                respBillRelationClassBody = new BillRelationClassVo();
                            }

                            BillClassDetailVo respBillClassDetailBody = this.generateClassDetailBody(billRelation);
                            // String totalAmount = respBillClassDetailBody.getTotalAmount();

                            respBillRelationClassBody.addList(respBillClassDetailBody);
                            respBillRelationClassBody.setClassName(relationTypeEnum.name());
                            respBillRelationClassBody.setSort(relationTypeEnum.getSort());
                            respBillRelationClassBody.setTotal(total);
                            respBillRelationClassBody.setTotalPage(pages);
                            respBillRelationClassBody.setClassTotalAmount(DecimalUtil.bigDecimalToString(totalAmount));
                            income_classNameBodyMap.put(relationType, respBillRelationClassBody);
                        } else if (AbstractTypeEnum.Expenditure.equals(belongAbstractType)) {
                            BillRelationClassVo respBillRelationClassBody = expenditure_classNameBodyMap.get(relationType);
                            if (respBillRelationClassBody == null) {
                                respBillRelationClassBody = new BillRelationClassVo();
                            }

                            BillClassDetailVo respBillClassDetailBody = this.generateClassDetailBody(billRelation);
                            // String totalAmount = respBillClassDetailBody.getTotalAmount();

                            respBillRelationClassBody.addList(respBillClassDetailBody);
                            respBillRelationClassBody.setClassName(relationTypeEnum.name());
                            respBillRelationClassBody.setSort(relationTypeEnum.getSort());
                            respBillRelationClassBody.setTotal(total);
                            respBillRelationClassBody.setTotalPage(pages);
                            respBillRelationClassBody.setClassTotalAmount(DecimalUtil.bigDecimalToString(totalAmount));
                            expenditure_classNameBodyMap.put(relationType, respBillRelationClassBody);
                        }
                    }
                }
            }

            // 账单关系下收入分类数据
            List<BillRelationClassVo> incomeList = new ArrayList<>();
            // 账单关系下支出分类数据
            List<BillRelationClassVo> expenditureList = new ArrayList<>();
            if (income_classNameBodyMap.size() > 0) {
                List<BillRelationClassVo> temporaryList = new ArrayList<>();
                income_classNameBodyMap.forEach((key, value) -> {
                    temporaryList.add(value);
                });
                incomeList = temporaryList.stream().sorted(Comparator.comparing(BillRelationClassVo::getSort))
                    .collect(Collectors.toList());
            }

            if (expenditure_classNameBodyMap.size() > 0) {
                List<BillRelationClassVo> temporaryList = new ArrayList<>();
                expenditure_classNameBodyMap.forEach((key, value) -> {
                    temporaryList.add(value);
                });
                expenditureList = temporaryList.stream().sorted(Comparator.comparing(BillRelationClassVo::getSort))
                    .collect(Collectors.toList());
            }

            BillClassVo vo = new BillClassVo();
            vo.setIncomeList(incomeList);
            vo.setExpenditureList(expenditureList);
            return R.ok(vo);
        } else {
            return R.fail(ZSMallStatusCodeEnum.BILL_NOT_FOUND);
        }
    }

    /**
     * 查询账单分类列表（单类型翻页）
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public TableDataInfo<BillClassDetailVo> queryBillClassPage(BillBaseBo bo, PageQuery pageQuery) throws RStatusCodeException {
        String billNo = bo.getBillNo();
        String className = bo.getClassName();
        if (StrUtil.hasBlank(billNo, className)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.MISSING_VALUE_IN_THE_INTERFACE);
        }

        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager, TenantType.Supplier);
        String tenantId = loginUser.getTenantId();
        String tenantType = loginUser.getTenantType();

        Bill queryEntity = new Bill();
        if (TenantType.Supplier.name().equals(tenantType)) {
            queryEntity.setTenantId(tenantId);
        }
        queryEntity.setBillNo(billNo);

        List<Bill> billList = iBillService.queryByEntity(queryEntity);
        Bill bill = billList.get(0);
        Long billId = bill.getId();

        Page<BillRelation> resultPage = iBillRelationService.queryByBillIdAndRelationType(pageQuery.build(), billId, className);

        List<BillRelation> relationList = resultPage.getRecords();
        List<BillClassDetailVo> voList = new ArrayList<>();
        for (BillRelation billRelation : relationList) {
            voList.add(this.generateClassDetailBody(billRelation));
        }
        return TableDataInfo.build(voList, resultPage.getTotal());
    }

    /**
     * 导出账单列表
     *
     * @param bo
     */
    @Override
    public R<Void> exportBillList(BillListBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager, TenantType.Supplier);
        String tenantId = loginUser.getTenantId();
        String tenantType = loginUser.getTenantType();

        if (TenantType.Supplier.name().equals(tenantType)) {
            bo.setQueryType("UserCode");
            bo.setQueryValue(tenantId);
        }

        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            String headerLanguage = ServletUtils.getHeaderLanguage();

            String billDate = bo.getBillDate();
            final LocalDateTime reqLocalDateTimeBegin;
            final LocalDateTime reqLocalDateTimeEnd;
            if (StrUtil.isNotBlank(billDate)) {
                DateTime reqDate = DateUtil.parse(billDate, "yyyy-MM");
                reqLocalDateTimeBegin = DateUtil.toLocalDateTime(DateUtil.beginOfMonth(reqDate)).withNano(0);
                reqLocalDateTimeEnd = DateUtil.toLocalDateTime(DateUtil.endOfMonth(reqDate)).withNano(0);
            } else {
                reqLocalDateTimeBegin = null;
                reqLocalDateTimeEnd = null;
            }

            String fileName = StrUtil.format(FileNameConstants.BILL_LIST, DateUtil.format(new Date(), "yyMMdd-HH:mm:ss.SSSS"));

            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(DownloadTypePlusEnum.Bill);

            iDownloadRecordService.save(newRecord);

            ThreadUtil.execute(() -> {
                BigExcelWriter excelWriter = null;

                try {
                    List<String> titleName;
                    if (StringUtils.equals(headerLanguage, "zh_CN")) {
                        if (ObjectUtil.equals(tenantType, TenantType.Manager.name())) {
                            titleName =
                                CollUtil.newLinkedList("账单编号", "用户ID", "结算周期起始", "结算周期截止", "本期收入", "本期支出","本期循环保证金", "上期循环保证金", "本期总金额");
                        } else {
                            titleName =
                                CollUtil.newLinkedList("账单编号", "结算周期起始", "结算周期截止", "本期收入", "本期支出", "本期循环保证金", "上期循环保证金", "本期总金额");
                        }
                    } else {
                        if (ObjectUtil.equals(tenantType, TenantType.Manager.name())) {
                            titleName = CollUtil
                                .newLinkedList("Bill ID", "User ID", "Settlement Cycle Begin", "Settlement Cycle End", "Current Income",
                                    "Curent Expenses", "Current Circular Deposit", "Previous Circular Deposit", "Current Total Amount");
                        } else {
                            titleName = CollUtil
                                .newLinkedList("Bill ID", "Settlement Cycle Begin", "Settlement Cycle End", "Current Income",
                                    "Current Expenses", "Current Circular Deposit", "Previous Circular Deposit", "Current Total Amount");
                        }
                    }

                    List<Bill> billList =
                        iBillService.queryList(bo.getQueryType(), bo.getQueryValue(), reqLocalDateTimeBegin, reqLocalDateTimeEnd);

                    List<Map<String, Object>> rows = new ArrayList<>();
                    if (CollUtil.isNotEmpty(billList)) {
                        for (Bill bill : billList) {
                            Map<String, Object> dataMap = new LinkedHashMap<>();
                            int index = 0;

                            String billNo = bill.getBillNo();
                            LocalDateTime settlementCycleBegin = bill.getSettlementCycleBegin();
                            LocalDateTime settlementCycleEnd = bill.getSettlementCycleEnd();
                            BigDecimal currentIncome = bill.getCurrentIncome();
                            BigDecimal currentExpenditure = bill.getCurrentExpenditure();
                            BigDecimal previousCircularDeposit = bill.getPreviousCircularDeposit();
                            BigDecimal currentCircularDeposit = bill.getCurrentCircularDeposit();
                            BigDecimal currentTotalAmount = bill.getCurrentTotalAmount();

                            String settlementCycleBegin_Str = LocalDateTimeUtil.format(settlementCycleBegin, dateTimeFormatter);
                            String settlementCycleEnd_Str = LocalDateTimeUtil.format(settlementCycleEnd, dateTimeFormatter);

                            String currentIncome_Str = "+$" + DecimalUtil.bigDecimalToString(currentIncome);
                            String currentExpenditure_Str = "-$" + DecimalUtil.bigDecimalToString(currentExpenditure);
                            String previousCircularDeposit_Str = "$" + DecimalUtil.bigDecimalToString(previousCircularDeposit);
                            String currentCircularDeposit_Str = "$" + DecimalUtil.bigDecimalToString(currentCircularDeposit);
                            String currentTotalAmount_Str = "$" + DecimalUtil.bigDecimalToString(currentTotalAmount);

                            dataMap.put(titleName.get(index++), billNo);

                            if (ObjectUtil.equals(tenantType, TenantType.Manager.name())) {
                                dataMap.put(titleName.get(index++), bill.getTenantId());
                            }

                            dataMap.put(titleName.get(index++), settlementCycleBegin_Str);
                            dataMap.put(titleName.get(index++), settlementCycleEnd_Str);
                            dataMap.put(titleName.get(index++), currentIncome_Str);
                            dataMap.put(titleName.get(index++), currentExpenditure_Str);
                            dataMap.put(titleName.get(index++), currentCircularDeposit_Str);
                            dataMap.put(titleName.get(index++), previousCircularDeposit_Str);
                            dataMap.put(titleName.get(index), currentTotalAmount_Str);
                            rows.add(dataMap);
                        }
                    }

                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFile = tempSavePath + File.separator + UUID.fastUUID().toString(true) + ".xlsx";

                    excelWriter = ExcelUtil.getBigWriter();
                    excelWriter.write(rows, true);
                    excelWriter.autoSizeColumnAll();
                    File file = FileUtil.newFile(tempFile);
                    excelWriter.flush(file);

                    @Cleanup InputStream inputStream = FileUtil.getInputStream(file);
                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    newRecord.setFileSize(StrUtil.toString(file.length()));
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);

                    FileUtil.del(file);
                } catch (Exception e) {
                    log.error("【导出账单列表】出现未知错误 {}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                } finally {
                    if (excelWriter != null) {
                        excelWriter.close();
                    }
                }
            });
            return R.ok();
        }
    }

    /**
     * 下载账单Pdf
     *
     * @param bo
     */
    @Override
    public R<Void> downloadBillPdf(BillBaseBo bo) throws Exception {

        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier, TenantType.Manager);
        String tenantType = loginUser.getTenantType();
        String billNo = bo.getBillNo();

        Bill queryEntity = new Bill();
        queryEntity.setBillNo(billNo);
        List<Bill> billList = iBillService.queryByEntity(queryEntity);

        if (CollUtil.isNotEmpty(billList)) {
            Bill bill = billList.get(0);
            GenerateStateEnum generateState = bill.getGenerateState();

            if (GenerateStateEnum.Generated.equals(generateState)) {
                Long ossId = bill.getOssId();

                @Cleanup InputStream inputStream = OSSObtainEvent.obtainFileInputStream(ossId);
                if (inputStream == null) {
                    return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_BILL_PDF_GENERATED_FAILED);
                }

                // 新文件key
                String fileName = new StringBuilder(billNo).append("-").append("PDF").append("-").append(UUID.fastUUID().toString(true)).append(".pdf").toString();
                OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                SpringUtils.publishEvent(uploadEvent);
                SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                // 创建新的下载记录
                DownloadRecord newRecord = new DownloadRecord();
                newRecord.setOssId(sysOssVo.getOssId());
                newRecord.setFileName(sysOssVo.getFileName());
                newRecord.setDownloadType(DownloadTypePlusEnum.BillDetail);
                newRecord.setFileSaveKey(sysOssVo.getFileName());
                newRecord.setFileUrl(sysOssVo.getUrl());
                newRecord.setDownloadQuery(billNo);
                newRecord.setRecordState(RecordStateEnum.Ready);
                iDownloadRecordService.save(newRecord);
            } else if (GenerateStateEnum.NotGenerated.equals(generateState)) {
                // 未生成，报错提示
                return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_BILL_PDF_NOT_GENERATED);
            } else if (GenerateStateEnum.Generating.equals(generateState)) {
                // 生成中，报错提示
                return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_BILL_PDF_GENERATING);
            } else if (GenerateStateEnum.Failed.equals(generateState)) {
                // 生成失败，报错提示
                return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_BILL_PDF_GENERATED_FAILED);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.BILL_NOT_FOUND);
        }

        return R.ok();
    }

    /**
     * 补充账单摘要
     *
     * @param bo
     */
    @Override
    public R<Void> supplementBillAbstract(JSONObject bo) {
        return null;
    }

    /**
     * 生成账单分类详情
     * @param billRelation
     * @return
     */
    private BillClassDetailVo generateClassDetailBody(BillRelation billRelation) {
        Boolean isAdmin=LoginHelper.getTenantType().equals(TenantType.Manager.name());
        Long billRelationId = billRelation.getId();
        Long targetId = billRelation.getTargetId();
        RelationTypeEnum relationType = billRelation.getRelationType();

        String classBase = "com.zsmall.system.entity.domain.vo.bill.billclass.";

        List<BillRelationDetail> billRelationDetailList = iBillRelationDetailService.queryByBillRelationId(billRelationId);
        Object newInstance = ReflectUtil.newInstance(classBase + "BillClass" + relationType.name() + "Vo");

        if (CollUtil.isNotEmpty(billRelationDetailList)) {
            for (BillRelationDetail billRelationDetailEntity : billRelationDetailList) {
                RelationFieldTypeEnum fieldTypeEnum = billRelationDetailEntity.getFieldType();
                String fieldValue = billRelationDetailEntity.getFieldValue();
                ReflectUtil.setFieldValue(newInstance, fieldTypeEnum.getFieldName(), fieldValue);
                //封装供应商ID和分销商ID
                if (isAdmin && fieldTypeEnum.name().equals(RelationFieldTypeEnum.OrderNo.name())){
                    LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    orderItemLambdaQueryWrapper.select(OrderItem::getId,OrderItem::getTenantId,OrderItem::getSupplierTenantId,OrderItem::getOrderNo);
                    orderItemLambdaQueryWrapper.eq(ObjectUtil.isNotEmpty(fieldValue),OrderItem::getOrderNo,fieldValue);
                    orderItemLambdaQueryWrapper.eq(OrderItem::getDelFlag,'0');
                    OrderItem orderItem = orderItemMapper.selectOneNotTenant(orderItemLambdaQueryWrapper);
                    if (ObjectUtil.isNotEmpty(orderItem)){
                        ReflectUtil.setFieldValue(newInstance, "tenantId", orderItem.getTenantId());
                        ReflectUtil.setFieldValue(newInstance, "supplierTenantId", orderItem.getSupplierTenantId());
                    }
                }

            }
        }
        return (BillClassDetailVo) newInstance;
    }

    @Override
    public R<Void> exportNew(String startDate, String endDate, HttpServletResponse response) {
        try {
            exportBillListDtoArrayList(startDate,endDate,response);
        }catch (Exception e){
            log.error("导出账单列表出现未知错误 {}", e.getMessage(), e);
        }
     return null;
    }

    private void exportBillListDtoArrayList(String startDate, String endDate,HttpServletResponse response) throws IOException {
       ServletOutputStream singleOutputStream = response.getOutputStream();
       ExcelWriter excelWriter = EasyExcel.write(singleOutputStream).build();
        com.hengjian.common.excel.utils.ExcelUtil.resetResponse("账单导出", response, false);
        BillMapper baseMapper = iBillService.getBaseMapper();
        //查找所有的Bill
        List<BillRelation> billRelationList = TenantHelper.ignore(() -> baseMapper.getAllBillRelation(startDate, endDate), TenantType.Supplier, TenantType.Manager);
        HashMap<Long, BillRelation> billRelationMap = new HashMap<>();
        billRelationList.forEach(s -> {
            billRelationMap.put(s.getBillId(), s);
        });
        Set<Long> bllIdSet = billRelationMap.keySet();
        ArrayList<BillExportListDto> billExportListDtoArrayList = new ArrayList<>();
        for (Long i : bllIdSet) {
            HashSet<Long> billIdSet = new HashSet<>();
            billIdSet.add(i);
            //根据billId 查找下面的BillRelationDetail
            List<String> allBillRelationDetailsByBillIdSet = baseMapper.getAllBillRelationDetailsByBillIdSet(billIdSet);
            //封装信息
            ArrayList<String> itemNoList = new ArrayList<>();
            allBillRelationDetailsByBillIdSet.forEach(s -> {
                System.out.println(s);
                Map<String, String> stringMap = parseStringToMap(s);
                BillExportListDto billExportListDtos = new BillExportListDto();
                //获取当前订单的操作类型
                BillRelation billRelation = billRelationMap.get(i);
                String OrderNo = stringMap.get("OrderNo");
                billExportListDtos.setOrderNo(OrderNo);
                itemNoList.add(OrderNo);
                billExportListDtos.setCreateDateTime(stringMap.get("CreateDateTime"));
                billExportListDtos.setItemNo(stringMap.get("ItemNo"));
                billExportListDtos.setTotalAmount(stringMap.get("TotalAmount"));
                if (billRelation.getRelationType().equals(RelationTypeEnum.OrderRefund)) {
                    billExportListDtos.setOrderType("退款单");
                } else {
                    billExportListDtos.setOrderType("支付单");
                    billExportListDtos.setProductQuantity(stringMap.get("ProductQuantity"));
                    billExportListDtos.setProductAmount(stringMap.get("ProductAmount"));
                    billExportListDtos.setOperationFee(stringMap.get("OperationFee"));
                    billExportListDtos.setFinalDeliveryFee(stringMap.get("FinalDeliveryFee"));
                }
                billExportListDtoArrayList.add(billExportListDtos);
            });
            //处理单据
            for (int h = 1; h < billExportListDtoArrayList.size(); h++) {
                if (billExportListDtoArrayList.get(h).getOrderNo() .equals(billExportListDtoArrayList.get(h-1).getOrderNo())) {
                    billExportListDtoArrayList.get(h).setOrderType("退款单");
                }
            }
            //批量查询关联关系
            List<OrderItemShippingRecord> ignore = TenantHelper.ignore(() -> orderItemShippingRecordMapper.getAllOrderItemShippingRecord(itemNoList), TenantType.Supplier, TenantType.Manager);
            if (CollectionUtil.isNotEmpty(ignore)){
                for (OrderItemShippingRecord orderItemShippingRecord : ignore) {
                    for (BillExportListDto billExportListDto : billExportListDtoArrayList) {
                        if (ObjectUtil.equals(billExportListDto.getOrderNo(),orderItemShippingRecord.getOrderNo())){
                            billExportListDto.setErpChannelOrderNo(orderItemShippingRecord.getShippingNo());
                        }
                    }
                }
            }

            synchronized (this) {
                //写入表格
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "第" + 0 + "页数据")
                                                 // 这里放入动态头
                                                 .head(BillExportListDto.class)
                                                 //传入样式
                                                 .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                                 .registerConverter(new ExcelBigNumberConvert())
                                                 // 当然这里数据也可以用 List<List<String>> 去传入
                                                 .build();
                excelWriter.write(billExportListDtoArrayList, writeSheet);
            }
        }
        excelWriter.finish();
    }

    public static Map<String, String> parseStringToMap(String input) {
        // 创建一个空的HashMap来存储结果
        Map<String, String> result = new HashMap<>();

        // 定义正则表达式来匹配键值对
        Pattern pattern = Pattern.compile("([^,]+?):([^,]+?)(?=,|$)");
        Matcher matcher = pattern.matcher(input);

        // 遍历所有匹配的键值对
        while (matcher.find()) {
            // 提取键和值
            String key = matcher.group(1).trim();
            String value = matcher.group(2).trim();

            // 将键和值存储到HashMap中
            result.put(key, value);
        }

        return result;
    }


    @Override
    public R<Void> billExport(BillExportquery billExportquery,
                              HttpServletResponse response)  {
        boolean isAdmin= ObjectUtil.equals(LoginHelper.getTenantType(),TenantType.Manager.name());
        String startDate = billExportquery.getStartDate();
        String endDate = billExportquery.getEndDate();
        if (ObjectUtil.isNotEmpty(startDate)){
            startDate=startDate+"-01";
        }
        if (ObjectUtil.isNotEmpty(endDate)){
            DateTime parse = DateUtil.parse(endDate, "yyyy-MM");
            int lastDay = parse.monthEnum().getLastDay(parse.isLeapYear());
            //处理时间
            endDate=endDate+"-"+lastDay;
        }

        ExcelWriter excelWriter = null;
        try {
            com.hengjian.common.excel.utils.ExcelUtil.resetResponse("账单导出",response,false);
             excelWriter = EasyExcel.write(response.getOutputStream()).build();
             //导出账单和明细
            exportBill(startDate, endDate, billExportquery.getBillNo(), billExportquery.getTenantId(),excelWriter,isAdmin);

        }catch (Exception e){
            log.error("导出账单列表出现未知错误 {}", e.getMessage(), e);
        }finally {
            excelWriter.finish();
        }
        return null;
    }



    /**
     * 导出账单汇总
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param billNo 订单编号集合
     * @param excelWriter
     * @param isAdmin
     * @return
     */
    public  void exportBill(String startDate, String endDate, List<String> billNo,String tenantId,ExcelWriter excelWriter,Boolean isAdmin ){
        List<String> tenantIds = new ArrayList<>();
        if (isAdmin) {
            if (StringUtils.isEmpty(tenantId)){
                List<SysTenant> sysTenantList = sysTenantMapper.selectList();
                tenantIds = sysTenantList.stream().map(SysTenant::getTenantId).collect(Collectors.toList());
            }else {
                tenantIds = (Collections.singletonList(tenantId));
            }
        } else {
            tenantIds = (Collections.singletonList(LoginHelper.getTenantId()));
        }

        BillMapper baseMapper = iBillService.getBaseMapper();
        LambdaQueryWrapper<Bill> billLambdaQueryWrapper = new LambdaQueryWrapper<>();
        billLambdaQueryWrapper.ge(ObjectUtil.isNotEmpty(startDate), Bill::getSettlementCycleBegin, startDate);
        billLambdaQueryWrapper.le(ObjectUtil.isNotEmpty(endDate), Bill::getSettlementCycleBegin, endDate);
        billLambdaQueryWrapper.in(CollUtil.isNotEmpty(billNo), Bill::getBillNo, billNo);
        billLambdaQueryWrapper.in(Bill::getTenantId, tenantIds);
        long pageSize=5000;
        long rowCount = TenantHelper.ignore(() -> baseMapper.selectCount(billLambdaQueryWrapper), TenantType.Supplier, TenantType.Manager);
        if (rowCount==0){
            WriteSheet writeSheet = EasyExcel.writerSheet(1, "账单汇总")//将数据映射到DownloadDTO实体类并响应到浏览器
                                             .head(isAdmin ? BillAdminExport.class : BillExport.class)
                                             //07的excel版本,节省内存
                                             //是否自动关闭输入流
                                             // 自定义列宽度，有数字会
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             //设置excel保护密码
                                             .build();
            excelWriter.write(new ArrayList<>(), writeSheet);
            WriteSheet writeSheet2 = EasyExcel.writerSheet(2, "账单明细")//将数据映射到DownloadDTO实体类并响应到浏览器
                                             .head(isAdmin ? BillAdminRelationDetailDto.class : BillRelationDetailDto.class)
                                             //07的excel版本,节省内存
                                             //是否自动关闭输入流
                                             // 自定义列宽度，有数字会
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             //设置excel保护密码
                                             .build();
            excelWriter.write(new ArrayList<>(), writeSheet2);
            return;
        }
        long totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);
        for (long i = 1; i <= totalPage; i++) {
            long finalI = i;
            Page<Bill> billPage = TenantHelper.ignore(() -> baseMapper.selectPage(new Page<>(finalI, pageSize), billLambdaQueryWrapper), TenantType.Supplier, TenantType.Manager);
            List<Bill> billList = billPage.getRecords();
            //查找所有的Bill
        // billList = TenantHelper.ignore(() -> baseMapper.selectList(billLambdaQueryWrapper), TenantType.Supplier, TenantType.Manager);
            List<BillAdminExport> billAdminExports = new ArrayList<>();
            List<BillExport> billExports = new ArrayList<>();
            if (CollUtil.isNotEmpty(billList)) {
                billList.forEach(s -> {
                    BillAdminExport billExport = new BillAdminExport();
                    billExport.setId(s.getId());
                    billExport.setTenantId(s.getTenantId());
                    billExport.setBillNo(s.getBillNo());
                    billExport.setSettlementDate(DateUtil.format(s.getSettlementCycleBegin(), "yyyy-MM-dd HH:mm:ss") + "至 " + DateUtil.format(s.getSettlementCycleEnd(), "yyyy-MM-dd HH:mm:ss"));
                    billExport.setCurrentIncome(s.getCurrentIncome());
                    billExport.setCurrentExpenditure(s.getCurrentExpenditure());
                    billExport.setPreviousCircularDeposit(s.getPreviousCircularDeposit());
                    billExport.setCurrentCircularDeposit(s.getCurrentCircularDeposit());
                    billExport.setCurrentTotalAmount(s.getCurrentTotalAmount());
                    billExport.setBillState(s.getBillState().name().equals("Unsettled") ? "未结算" : "已结算");
                    billAdminExports.add(billExport);
                });
            }

            //对象拷贝
            if (!isAdmin){
                billExports= BillConvert.INSTANCE.convertBillAdminTOBIll(billAdminExports);
            }
            WriteSheet writeSheet = EasyExcel.writerSheet(1, "账单汇总")//将数据映射到DownloadDTO实体类并响应到浏览器
                                             .head(isAdmin ? BillAdminExport.class : BillExport.class)
                                             //07的excel版本,节省内存
                                             //是否自动关闭输入流
                                             // 自定义列宽度，有数字会
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             //设置excel保护密码
                                             .build();
            excelWriter.write(isAdmin ? billAdminExports : billExports, writeSheet);
            //导出明细
            exportBillRelationDetail(billAdminExports,isAdmin,excelWriter);
        }

    }

    /**
     * 导出账单明细
     *
     * @param billAdminExports
     * @param isAdmin
     * @param excelWriter
     */
    private void exportBillRelationDetail(List<BillAdminExport> billAdminExports, boolean isAdmin,
                                          ExcelWriter excelWriter) {
        BillMapper billMapper = iBillService.getBaseMapper();
        Set<Long> billIdSet = billAdminExports.stream().map(BillAdminExport::getId).collect(Collectors.toSet());
        Map<Long, String> billMap = billAdminExports.stream()
                                                    .collect(Collectors.toMap(BillAdminExport::getId, BillAdminExport::getBillNo));
        //只是利用这个对象传值
        List<Bill> billRelationDetal = TenantHelper.ignore(() -> billMapper.getBillRelationDetailByBillIDSet(billIdSet), TenantType.Supplier, TenantType.Manager);
        List<BillAdminRelationDetailDto> billAdminRelationDetailDtoList=new ArrayList<>();
        List<BillRelationDetailDto> billRelationDetailDtoList =new ArrayList<>();
        List<String> orderNoList=new ArrayList<>();
        billRelationDetal.stream().forEach(s->{
            Map<String, String> stringMap = parseStringToMap(s.getBillCycleNo());
            BillAdminRelationDetailDto billAdminRelationDetailDto=new BillAdminRelationDetailDto();
            String OrderNo = stringMap.get("OrderNo");
            orderNoList.add(OrderNo);
            billAdminRelationDetailDto.setOrderNo(OrderNo);
            billAdminRelationDetailDto.setBillNo(billMap.get(s.getPreviousBillId()));
            billAdminRelationDetailDto.setCreateDateTime(stringMap.get("CreateDateTime"));
            billAdminRelationDetailDto.setItemNo(stringMap.get("ItemNo"));
            billAdminRelationDetailDto.setTotalAmount(stringMap.get("TotalAmount"));
            billAdminRelationDetailDto.setProductQuantity(stringMap.get("ProductQuantity"));
            billAdminRelationDetailDto.setProductAmount(stringMap.get("ProductAmount"));
            billAdminRelationDetailDto.setOperationFee(stringMap.get("OperationFee"));
            billAdminRelationDetailDto.setFinalDeliveryFee(stringMap.get("FinalDeliveryFee"));
            if (ObjectUtil.equals("Income",s.getBillNo())) {
                billAdminRelationDetailDto.setOrderType("收入");
            } else {
                billAdminRelationDetailDto.setOrderType("支出");
            }
            billAdminRelationDetailDtoList.add(billAdminRelationDetailDto);
        });
        if (isAdmin){
            //处理管理员的供应商/分销商信息
            LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderItemLambdaQueryWrapper.select(OrderItem::getId,OrderItem::getTenantId,OrderItem::getSupplierTenantId,OrderItem::getOrderNo);
            orderItemLambdaQueryWrapper.in(CollectionUtil.isNotEmpty(orderNoList),OrderItem::getOrderNo,orderNoList);
            orderItemLambdaQueryWrapper.eq(OrderItem::getDelFlag,'0');
            List<OrderItem> orderItems = orderItemMapper.selectListNotTenant(orderItemLambdaQueryWrapper);
            for (BillAdminRelationDetailDto billAdminRelationDetailDto : billAdminRelationDetailDtoList) {
                for (OrderItem orderItem : orderItems) {
                    if (billAdminRelationDetailDto.getOrderNo().equals(orderItem.getOrderNo())){
                        billAdminRelationDetailDto.setTenantId(orderItem.getTenantId());
                        billAdminRelationDetailDto.setSupplierTenantId(orderItem.getSupplierTenantId());
                    }
                }
            }
        }
        if (!isAdmin){
            billRelationDetailDtoList = BillConvert.INSTANCE.convertBillRelationTOBIll(billAdminRelationDetailDtoList);
        }
        WriteSheet writeSheet = EasyExcel.writerSheet(2, "账单明细")//将数据映射到DownloadDTO实体类并响应到浏览器
                                         .head(isAdmin ? BillAdminRelationDetailDto.class : BillRelationDetailDto.class)
                                         //07的excel版本,节省内存
                                         //是否自动关闭输入流
                                         // 自定义列宽度，有数字会
                                         .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                         //设置excel保护密码
                                         .build();
        excelWriter.write(isAdmin ? billAdminRelationDetailDtoList : billRelationDetailDtoList, writeSheet);
        log.info(billRelationDetailDtoList.toString());
    }


}
