package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.bill.BillStateEnum;
import com.zsmall.common.enums.bill.WithdrawalStateEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.*;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.system.biz.service.WithdrawalRecordService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.bo.receipt.TenantReceiptAccountBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptBo;
import com.zsmall.system.entity.domain.bo.transaction.WithdrawalRecordBo;
import com.zsmall.system.entity.domain.bo.transaction.WithdrawalRecordReviewQueryBo;
import com.zsmall.system.entity.domain.vo.bill.BillListVo;
import com.zsmall.system.entity.domain.vo.receipt.*;
import com.zsmall.system.entity.domain.vo.transaction.*;
import com.zsmall.system.entity.iservice.ITenantReceiptAccountService;
import com.zsmall.system.entity.mapper.*;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提现记录-实现类
 *
 * <AUTHOR> @date 2023年7月6日
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WithdrawalRecordServiceImpl implements WithdrawalRecordService {

    private static final DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final TransactionReceiptMapper transactionReceiptMapper;
    private final TransactionReceiptAttachmentMapper transactionReceiptAttachmentMapper;
    private final TransactionRecordMapper transactionRecordMapper;
    private final TenantReceiptAccountMapper tenantReceiptAccountMapper;
    private final TenantReceiptAccountCreditMapper tenantReceiptAccountCreditMapper;
    private final TenantReceiptAccountPayoneerMapper tenantReceiptAccountPayoneerMapper;
    private final BillMapper billMapper;
    private final TransactionsBillMapper transactionsBillMapper;
    private final ITenantReceiptAccountService iTenantReceiptAccountService;
    private final WorldLocationMapper worldLocationMapper;


    @Override
    public WithdrawalRecordVo getWithdrawalRecordPage(TransactionReceiptBo bo, PageQuery pageQuery) {
        log.info("进入【翻页查询提现记录】方法，{} {}", JSONUtil.toJsonStr(bo), JSONUtil.toJsonStr(pageQuery));

        BigDecimal withdrawnAmount = BigDecimal.ZERO;
        //供应商可提现金额为已结算未提现的账单金额
        LambdaQueryWrapper<Bill> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Bill::getWithdrawalState, WithdrawalStateEnum.NotWithdrawal)
            .eq(Bill::getBillState, BillStateEnum.Settled);
        List<Bill> billEntities = billMapper.selectList(lqw);
        List<Bill> bs = billEntities.stream().filter(b -> !NumberUtil.equals(b.getCurrentTotalAmount(), BigDecimal.ZERO)).collect(Collectors.toList());
        if (CollUtil.size(bs) > 0) {
            withdrawnAmount = bs.stream().map(Bill::getCurrentTotalAmount).reduce(BigDecimal::add).get();
        }

        WithdrawalRecordVo vo = new WithdrawalRecordVo();
        vo.setWithdrawnAmount(withdrawnAmount.setScale(2, RoundingMode.HALF_UP));

        LambdaQueryWrapper<TransactionReceipt> lqw1 = new LambdaQueryWrapper<>();
        // 提现编码
        String transactionReceiptNo = bo.getTransactionReceiptNo();
        lqw1.eq(StrUtil.isNotBlank(transactionReceiptNo), TransactionReceipt::getTransactionReceiptNo, transactionReceiptNo);
        // 提现收款账户
//        String accountName = bo.getAccountName();
        setTransactionReceiptAccountNameLwq(lqw1, bo.getAccountName());
        // 申请时间
        String createTimeString = bo.getCreateTimeString();
        if (StrUtil.isNotBlank(createTimeString)) {
            lqw1.ge(TransactionReceipt::getCreateTime, createTimeString + " 00:00:00");
            lqw1.le(TransactionReceipt::getCreateTime, createTimeString + " 23:59:59");
        }
        // 到账时间
        String receiptTimeString = bo.getReceiptTimeString();
        if (StrUtil.isNotBlank(receiptTimeString)) {
            lqw1.ge(TransactionReceipt::getReceiptTime, receiptTimeString + " 00:00:00");
            lqw1.le(TransactionReceipt::getReceiptTime, receiptTimeString + " 23:59:59");
        }
        // 收款方式
        String transactionMethod = bo.getTransactionMethod();
        if (StrUtil.isNotBlank(transactionMethod)) {
            lqw1.eq(TransactionReceipt::getTransactionMethod, TransactionMethodEnum.valueOf(transactionMethod));
        }
        // 状态
        String reviewState = bo.getReviewState();
        if (StrUtil.isNotBlank(reviewState)) {
            lqw1.eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.valueOf(reviewState));
        }
        lqw1.orderByDesc(TransactionReceipt::getCreateTime);
        Page<TransactionReceiptVo> transactionReceiptPage = transactionReceiptMapper.selectVoPage(pageQuery.build(), lqw1);
        List<TransactionReceiptVo> records = transactionReceiptPage.getRecords();
        for (TransactionReceiptVo record : records) {
            Long receiptAccountId = record.getReceiptAccountId();
            String transactionMethod1 = record.getTransactionMethod();
            String name = getAccountName(receiptAccountId, transactionMethod1);
            record.setAccountName(name);
        }
        vo.setWithdrawnRecord(TableDataInfo.build(transactionReceiptPage));
        return vo;
    }

    /**
     * 设置LambdaQueryWrapper AccountName查询条件
     * @param lqw
     * @param accountName
     */
    private static void setTransactionReceiptAccountNameLwq(LambdaQueryWrapper<TransactionReceipt> lqw, String accountName) {
        lqw.exists(StrUtil.isNotBlank(accountName), "select 1 from tenant_receipt_account_credit ac " +
            "where ac.receipt_account_id = transaction_receipt.receipt_account_id and ac.account_number like CONCAT('%', {0}, '%') " +
            "or exists (select 1 from tenant_receipt_account_payoneer ap where ap.receipt_account_id = transaction_receipt.receipt_account_id " +
            "and ap.payoneer_email like CONCAT('%', {1}, '%'))", accountName, accountName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> submitWithdrawalApplication(WithdrawalRecordBo bo) {
        log.info("进入【提交提现申请信息】方法，{}", JSONUtil.toJsonStr(bo));
        String tenantId = LoginHelper.getTenantId();

        String accountCode = bo.getAccountCode();
        String password = bo.getPassword();
        BigDecimal receiptAmount = bo.getReceiptAmount();
        List<String> billNoList = bo.getBillNoList();
        String note = bo.getNote();

        //密码校验
        boolean success = SystemEventUtils.verifyPassword(password);
        if (!success) {
            return R.fail(ZSMallStatusCodeEnum.PASSWORD_ERROR);
        }

        LambdaQueryWrapper<TenantReceiptAccount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantReceiptAccount::getAccountCode, accountCode)
            .eq(TenantReceiptAccount::getAccountStatus, 1);
        List<TenantReceiptAccount> tenantReceiptAccounts = tenantReceiptAccountMapper.selectList(lqw);
        if (CollUtil.isEmpty(tenantReceiptAccounts)) {
            return R.fail(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_NOT_EXIST_OR_DISABLED_ERROR);
        }

        BigDecimal billAccountTotal = BigDecimal.ZERO;
        List<Bill> billEntityList = new ArrayList<>();
        if (CollUtil.size(billNoList) > 0) {
            LambdaQueryWrapper<Bill> billLqw = new LambdaQueryWrapper<>();
            billLqw.in(Bill::getBillNo, billNoList);
            billEntityList = billMapper.selectList(billLqw);
            billAccountTotal = billEntityList.stream().map(Bill::getCurrentTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        }
        log.info("billAccountTotal = {}", billAccountTotal);
        if (!NumberUtil.equals(billAccountTotal, receiptAmount)) {
            return R.fail(ZSMallStatusCodeEnum.RECEIPT_AMOUNT_NOT_MATCH);
        }
        TenantReceiptAccount receiptAccount = tenantReceiptAccounts.get(0);
        log.info("receiptAccount = {}", JSONUtil.toJsonStr(receiptAccount));
        Long receiptAccountId = receiptAccount.getId();
        ReceiptAccountTypeEnum accountTypeEnum = receiptAccount.getAccountType();
        TransactionMethodEnum paymentMethodType;
        if (ObjectUtil.equals(accountTypeEnum, ReceiptAccountTypeEnum.Credit)) {
            paymentMethodType = TransactionMethodEnum.DirectBankTransfer;
        } else {
            paymentMethodType = TransactionMethodEnum.OnlinePayoneer;
        }

        // 保存提现交易记录
        TransactionRecord record = new TransactionRecord();
        record.setTenantId(tenantId);
        record.setTransactionAmount(billAccountTotal);
        record.setBeforeBalance(BigDecimal.ZERO);
        record.setAfterBalance(BigDecimal.ZERO);
        record.setTransactionType(TransactionTypeEnum.Withdrawal);
        record.setTransactionSubType(TransactionSubTypeEnum.Withdrawal);
        String transactionNo;
        try {
            transactionNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo);
        } catch (RStatusCodeException rStatusCodeException) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(rStatusCodeException.getStatusCode());
        }
        record.setTransactionNo(transactionNo);
        record.setTransactionNote(note);
        record.setTransactionState(TransactionStateEnum.Processing);
        transactionRecordMapper.insert(record);

        //如果存在账单数据，则添加账单交易记录关系和修改账单状态
        if (CollUtil.size(billEntityList) > 0) {
            List<TransactionsBill> tbList = new ArrayList<>();
            List<Bill> billList = new ArrayList<>();
            for (Bill bill : billEntityList) {
                bill.setWithdrawalState(WithdrawalStateEnum.Withdrawing);
                billList.add(bill);
                TransactionsBill tbEntity = new TransactionsBill();
                tbEntity.setBillId(bill.getId());
                tbEntity.setTransactionsId(record.getId());
                tbList.add(tbEntity);
            }
            if (CollUtil.size(tbList) > 0) {
                boolean save = TenantHelper.ignore(() -> transactionsBillMapper.insertBatch(tbList));
                if (!save) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.fail(ZSMallStatusCodeEnum.RECEIPT_TRANSACTION_RECORD_SAVE_ERROR.getMessage());
                }
            }
            if (CollUtil.size(billList) > 0) {
                if (!billMapper.insertOrUpdateBatch(billList)) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.fail("修改账单提现状态信息失败！");
                }
            }
        }

        TransactionReceipt receipt = new TransactionReceipt();
        receipt.setTenantId(tenantId);
        String transactionReceiptNo;
        try {
            transactionReceiptNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionReceiptNo);
        } catch (RStatusCodeException rStatusCodeException) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(rStatusCodeException.getStatusCode());
        }
        receipt.setTransactionReceiptNo(transactionReceiptNo);
        receipt.setTransactionType(TransactionTypeEnum.Withdrawal);
        receipt.setTransactionsId(record.getId());
        receipt.setReceiptAccountId(receiptAccountId);
        receipt.setTransactionAmount(receiptAmount);
        receipt.setNote(note);
        receipt.setTransactionMethod(paymentMethodType);
        receipt.setReviewState(ReceiptReviewStateEnum.Pending);
        int insert = transactionReceiptMapper.insert(receipt);
        if (insert <= 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(ZSMallStatusCodeEnum.RECEIPT_RECORD_SAVE_ERROR);
        }
        return R.ok();
    }

    @Override
    public R<WithdrawalRecordDetailVo> getWithdrawalRecordDetail(TransactionReceiptBo bo) {
        log.info("进入【查询提现记录详情】方法，{}", JSONUtil.toJsonStr(bo));
        // 判断是否是管理员
        boolean isManager = ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Manager);

        String receiptNo = bo.getTransactionReceiptNo();
        if (StrUtil.isBlank(receiptNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionReceipt::getTransactionReceiptNo, receiptNo)
            .eq(TransactionReceipt::getTransactionType, TransactionTypeEnum.Withdrawal);
        TransactionReceipt receipt = TenantHelper.ignore(() -> transactionReceiptMapper.selectOne(lqw));

        log.info("receipt == null ? {}", receipt == null);
        WithdrawalRecordDetailVo respBody = new WithdrawalRecordDetailVo();
        List<WithdrawalRecordDetailVo.Attachment> respAttachmentList = new ArrayList<>();
        if (receipt != null) {
            String paymentReceiptNo = receipt.getTransactionReceiptNo();
            Date createDateTime = receipt.getCreateTime();
            BigDecimal amount = receipt.getTransactionAmount();
            ReceiptReviewStateEnum reviewStatus = receipt.getReviewState();
            LocalDateTime receiptDate = receipt.getReceiptTime();
            String note = receipt.getNote();
            String noteMd = receipt.getNoteManager();
            Long receiptAccountId = receipt.getReceiptAccountId();
            String amountStr = amount.toPlainString();
            String applyDateTime = DateUtil.format(createDateTime, dateTimeFormatter);
            String destDateTime = LocalDateTimeUtil.format(receiptDate, dateTimeFormatter);

            if (receiptAccountId != null) {
                TenantReceiptAccount receiptAccount = TenantHelper.ignore(() -> tenantReceiptAccountMapper.selectById(receiptAccountId));
                ReceiptAccountTypeEnum accountType = receiptAccount.getAccountType();
                Integer accountStatus = receiptAccount.getAccountStatus();

                String accountNumberOrEmail;
                Long tenantReceiptAccountId = receiptAccount.getId();
                log.info("receiptAccountEntity = {}", JSONUtil.toJsonStr(receiptAccount));
                if (ObjectUtil.equals(accountType, ReceiptAccountTypeEnum.Credit)) {
                    LambdaQueryWrapper<TenantReceiptAccountCredit> lqw2 = new LambdaQueryWrapper<>();
                    lqw2.eq(TenantReceiptAccountCredit::getReceiptAccountId, tenantReceiptAccountId);
                    TenantReceiptAccountCreditVo credit = TenantHelper.ignore(() -> tenantReceiptAccountCreditMapper.selectVoOne(lqw2));

                    accountNumberOrEmail = credit.getAccountNumber();
                    if(accountNumberOrEmail.length() < 9) {
                        accountNumberOrEmail = DesensitizedUtil.desensitized(accountNumberOrEmail, DesensitizedUtil.DesensitizedType.FIRST_MASK);
                    } else {
                        accountNumberOrEmail = DesensitizedUtil.desensitized(accountNumberOrEmail, DesensitizedUtil.DesensitizedType.BANK_CARD);
                    }

                    String bankName = credit.getBankName();
                    String swiftCode = credit.getSwiftCode();
                    String accountName = credit.getAccountName();
                    String currency = credit.getCurrency();
                    String bankAddress = credit.getBankAddress();
                    String holderAddress = credit.getHolderAddress();

                    respBody.setAccountNumber(accountNumberOrEmail);
                    respBody.setBankName(bankName);
                    respBody.setSwiftCode(swiftCode);
                    respBody.setAccountName(accountName);
                    respBody.setCurrency(currency);
                    respBody.setBankAddress(bankAddress);
                    respBody.setHolderAddress(holderAddress);
                    Long countryId = credit.getCountryId();
                    respBody.setCountryId(countryId);

                    // 如果是管理员，显示真正内容
                    if(isManager) {
                        respBody.setAccountNumberPlatform(credit.getAccountNumber());
                    }
                } else {
                    LambdaQueryWrapper<TenantReceiptAccountPayoneer> lqw2 = new LambdaQueryWrapper<>();
                    lqw2.eq(TenantReceiptAccountPayoneer::getReceiptAccountId, tenantReceiptAccountId);
                    TenantReceiptAccountPayoneer payoneer = TenantHelper.ignore(() -> tenantReceiptAccountPayoneerMapper.selectOne(lqw2));
                    accountNumberOrEmail = DesensitizedUtil.desensitized(payoneer.getPayoneerEmail(), DesensitizedUtil.DesensitizedType.EMAIL);
                    respBody.setPayoneerEmail(accountNumberOrEmail);
                    // 如果是管理员，显示真正内容
                    if(isManager) {
                        respBody.setPayoneerEmailPlatform(payoneer.getPayoneerEmail());
                    }
                }
                respBody.setAccountStatus(accountStatus);
                respBody.setAccountType(accountType.getValue());
                //附件信息
                LambdaQueryWrapper<TransactionReceiptAttachment> attLqw = new LambdaQueryWrapper<>();
                attLqw.eq(TransactionReceiptAttachment::getTransactionReceiptNo, receiptNo);
                List<TransactionReceiptAttachment> attachmentList = transactionReceiptAttachmentMapper.selectList(attLqw);
                for (TransactionReceiptAttachment attachment : attachmentList) {
                    String fileName = attachment.getAttachmentName();
                    String showUrl = attachment.getAttachmentShowUrl();
                    String savePath = attachment.getAttachmentSavePath();
                    Long sort = attachment.getAttachmentSort();
                    String type = attachment.getAttachmentType();
                    AttachmentTypeEnum attachmentTypeEnum = AttachmentTypeEnum.valueOf(type);
                    WithdrawalRecordDetailVo.Attachment respAttachment = new WithdrawalRecordDetailVo.Attachment();
                    respAttachment.setShowUrl(showUrl);
                    respAttachment.setFileName(fileName);
                    respAttachment.setSavePath(savePath);
                    respAttachment.setSort(sort);
                    respAttachment.setType(attachmentTypeEnum.name());
                    respAttachmentList.add(respAttachment);
                }
            } else {
                String accountType = ReceiptAccountTypeEnum.Credit.name();
                String accountId = receipt.getAccountId();
                String accountName = receipt.getAccountName();
                respBody.setAccountType(accountType);
                respBody.setAccountNumber(accountId);
                respBody.setAccountName(accountName);
            }

            //填充账单列表响应信息
            Long transactionsId = receipt.getTransactionsId();

            LambdaQueryWrapper<TransactionsBill> tbLqw = new LambdaQueryWrapper<>();
            tbLqw.eq(TransactionsBill::getTransactionsId, transactionsId);
            List<TransactionsBill> transactionsBills = TenantHelper.ignore(() -> transactionsBillMapper.selectList(tbLqw));
            List<Long> billIds = transactionsBills.stream().map(TransactionsBill::getBillId).collect(Collectors.toList());

            List<BillListVo> results = new ArrayList<>();
            if (CollUtil.isNotEmpty(billIds)) {
                LambdaQueryWrapper<Bill> billLqw = new LambdaQueryWrapper<>();
                billLqw.in(Bill::getId, billIds);
                List<Bill> billEntities = TenantHelper.ignore(() -> billMapper.selectList(billLqw));
                for (Bill record : billEntities) {
                    String billNo = record.getBillNo();
                    LocalDateTime settlementCycleBegin = record.getSettlementCycleBegin();
                    LocalDateTime settlementCycleEnd = record.getSettlementCycleEnd();
                    BigDecimal currentTotalAmount = record.getCurrentTotalAmount();
                    BillListVo respBillListBody = new BillListVo();
                    String settlementCycleBegin_Str = LocalDateTimeUtil.format(settlementCycleBegin, dateTimeFormatter);
                    String settlementCycleEnd_Str = LocalDateTimeUtil.format(settlementCycleEnd, dateTimeFormatter);
                    String currentTotalAmount_Str = "$" + DecimalUtil.bigDecimalToString(currentTotalAmount);

                    respBillListBody.setBillNo(billNo);
                    respBillListBody.setSettlementCycle(settlementCycleBegin_Str + "至" + settlementCycleEnd_Str);
                    respBillListBody.setSettlementCycle_zh_CN(settlementCycleBegin_Str + "至" + settlementCycleEnd_Str);
                    respBillListBody.setSettlementCycle_en_US(settlementCycleBegin_Str + " to " + settlementCycleEnd_Str);
                    respBillListBody.setCurrentTotalAmount(currentTotalAmount_Str);
                    results.add(respBillListBody);
                }
            }

            respBody.setBillList(results);
            respBody.setReceiptNo(paymentReceiptNo);
            respBody.setApplyDateTime(applyDateTime);
            respBody.setReceiptAmount(amountStr);
            respBody.setReceiptStatus(reviewStatus.getValue());
            respBody.setReceiptDestDateTime(destDateTime);
            respBody.setNote(note);
            respBody.setNoteMd(noteMd);
            respBody.setAttachmentList(respAttachmentList);
            respBody.setTenantId(receipt.getTenantId());
        }
        return R.ok(respBody);
    }


    @Override
    public List<TransactionReceiptWithdrawalVo> queryList(TransactionReceiptBo bo) {
        LambdaQueryWrapper<TransactionReceipt> lqw1 = new LambdaQueryWrapper<>();
        // 提现编码
        String transactionReceiptNo = bo.getTransactionReceiptNo();
        lqw1.eq(StrUtil.isNotBlank(transactionReceiptNo), TransactionReceipt::getTransactionReceiptNo, transactionReceiptNo);
        // 提现收款账户
        String accountName = bo.getAccountName();
        setTransactionReceiptAccountNameLwq(lqw1, accountName);
        // 申请时间
        String createTimeString = bo.getCreateTimeString();
        if (StrUtil.isNotBlank(createTimeString)) {
            lqw1.ge(TransactionReceipt::getCreateTime, createTimeString + " 00:00:00");
            lqw1.le(TransactionReceipt::getCreateTime, createTimeString + " 23:59:59");
        }
        // 到账时间
        String receiptTimeString = bo.getReceiptTimeString();
        if (StrUtil.isNotBlank(receiptTimeString)) {
            lqw1.ge(TransactionReceipt::getReceiptTime, receiptTimeString + " 00:00:00");
            lqw1.le(TransactionReceipt::getReceiptTime, receiptTimeString + " 23:59:59");
        }
        // 收款方式
        String transactionMethod = bo.getTransactionMethod();
        if (StrUtil.isNotBlank(transactionMethod)) {
            lqw1.eq(TransactionReceipt::getTransactionMethod, TransactionMethodEnum.valueOf(transactionMethod));
        }
        // 状态
        String reviewState = bo.getReviewState();
        if (StrUtil.isNotBlank(reviewState)) {
            lqw1.eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.valueOf(reviewState));
        }
        lqw1.orderByDesc(TransactionReceipt::getCreateTime);
        List<TransactionReceiptVo> transactionReceiptVos = transactionReceiptMapper.selectVoList(lqw1);
        List<TransactionReceiptWithdrawalVo> vos = new ArrayList<>();
        for (TransactionReceiptVo vo : transactionReceiptVos) {
            TransactionReceiptWithdrawalVo wvo = BeanUtil.copyProperties(vo, TransactionReceiptWithdrawalVo.class);
            String transactionMethod1 = vo.getTransactionMethod();
            Long receiptAccountId = vo.getReceiptAccountId();
            String name = getAccountName(receiptAccountId, transactionMethod1);
            wvo.setAccountName(name);
            vos.add(wvo);
        }
        return vos;
    }

    /**
     * 根据提现账户Id和交易方式获取取现账户
     *
     * @param receiptAccountId
     * @param transactionMethod
     * @return
     */
    private String getAccountName(Long receiptAccountId, String transactionMethod) {
        String name = "";
        if (TransactionMethodEnum.DirectBankTransfer.getValue().equals(transactionMethod)) {
            LambdaQueryWrapper<TenantReceiptAccountCredit> lqw2 = new LambdaQueryWrapper<>();
            lqw2.eq(TenantReceiptAccountCredit::getReceiptAccountId, receiptAccountId);
            TenantReceiptAccountCreditVo credit = tenantReceiptAccountCreditMapper.selectVoOne(lqw2);

            name = credit.getAccountNumber();
            if (!ObjectUtil.equal(LoginHelper.getTenantTypeEnum(), TenantType.Manager)) {
                if(name.length() < 9) {
                    name = DesensitizedUtil.desensitized(name, DesensitizedUtil.DesensitizedType.FIRST_MASK);
                } else {
                    name = DesensitizedUtil.desensitized(name, DesensitizedUtil.DesensitizedType.BANK_CARD);
                }
            }
        } else if (TransactionMethodEnum.OnlinePayoneer.getValue().equals(transactionMethod)) {
            LambdaQueryWrapper<TenantReceiptAccountPayoneer> lqw2 = new LambdaQueryWrapper<>();
            lqw2.eq(TenantReceiptAccountPayoneer::getReceiptAccountId, receiptAccountId);
            TenantReceiptAccountPayoneer payoneer = tenantReceiptAccountPayoneerMapper.selectOne(lqw2);

            name = payoneer.getPayoneerEmail();
            if (!ObjectUtil.equal(LoginHelper.getTenantTypeEnum(), TenantType.Manager)) {
                name = DesensitizedUtil.desensitized(payoneer.getPayoneerEmail(), DesensitizedUtil.DesensitizedType.EMAIL);
            }
        }
        return name;
    }

    /**
     * 获取平台提现汇总金额
     *
     * @return
     */
    @Override
    public PlatformWithdrawalAmount getPlatformWithdrawalTotalAmount() {
        List<ReceiptReviewStateEnum> pendingList = Arrays.asList(ReceiptReviewStateEnum.Pending);
        List<ReceiptReviewStateEnum> approvedList = Arrays.asList(ReceiptReviewStateEnum.Accepted, ReceiptReviewStateEnum.Processing);
        BigDecimal totalPendingAmount = transactionReceiptMapper.sumAmountByTransactionTypeAndReviewStatus(TransactionTypeEnum.Withdrawal, pendingList);
        BigDecimal totalApprovedAmount = transactionReceiptMapper.sumAmountByTransactionTypeAndReviewStatus(TransactionTypeEnum.Withdrawal, approvedList);
        log.info("totalPendingAmount = {}, totalApprovedAmount = {} ", totalPendingAmount, totalApprovedAmount);

        return new PlatformWithdrawalAmount(DecimalUtil.bigDecimalToString(totalPendingAmount), DecimalUtil.bigDecimalToString(totalApprovedAmount));
    }

    /**
     * 翻页查询平台提现记录
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<TransactionReceiptWithdrawalPlatformVo> getPlatformWithdrawalRecordPage(WithdrawalRecordReviewQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TransactionReceipt> lqw = builderPlatformReceiptWithdrawalLqw(bo);
        Page<TransactionReceipt> result = TenantHelper.ignore(() -> transactionReceiptMapper.selectPage(pageQuery.build(), lqw));
        long total = result.getTotal();
        List<TransactionReceipt> records = result.getRecords();

        List<TransactionReceiptWithdrawalPlatformVo> withdrawalPlatformVos = convertWithdrawalPlatformVos(records);
        return TableDataInfo.build(withdrawalPlatformVos, total);
    }

    /**
     * 构建查询条件
     *
     * @param bo
     * @return
     */
    @NotNull
    private LambdaQueryWrapper<TransactionReceipt> builderPlatformReceiptWithdrawalLqw(WithdrawalRecordReviewQueryBo bo) {
        LambdaQueryWrapper<TransactionReceipt> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentReceiptNo()), TransactionReceipt::getTransactionReceiptNo, bo.getPaymentReceiptNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), TransactionReceipt::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiptStatus()), TransactionReceipt::getReviewState, bo.getReceiptStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethodType()), TransactionReceipt::getTransactionMethod, bo.getPaymentMethodType());

        List<String> receiptApplyDate = bo.getReceiptApplyDate();
        if (CollUtil.isNotEmpty(receiptApplyDate)) {
            String start = receiptApplyDate.get(0);
            String end = receiptApplyDate.get(1);
            lqw.ge(TransactionReceipt::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(start)))
                .le(TransactionReceipt::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(end)));
        }
        List<String> receiptDestDate = bo.getReceiptDestDate();
        if (CollUtil.isNotEmpty(receiptDestDate)) {
            String start = receiptDestDate.get(0);
            String end = receiptDestDate.get(1);
            lqw.ge(TransactionReceipt::getReceiptTime, DateUtil.beginOfDay(DateUtil.parseDate(start)))
                .le(TransactionReceipt::getReceiptTime, DateUtil.endOfDay(DateUtil.parseDate(end)));
        }

        setTransactionReceiptAccountNameLwq(lqw, bo.getReceiptAccount());
//        String receiptAccount = bo.getReceiptAccount();
//        lqw.exists(StringUtils.isNotBlank(receiptAccount),
//            "select 1 from tenant_receipt_account tra left join tenant_receipt_account_credit trac on tra.id = trac.receipt_account_id " +
//                "left join tenant_receipt_account_payoneer trap on tra.id = trap.receipt_account_id where tra.id = transaction_receipt.receipt_account_id " +
//                "and tra.del_flag = '0' and (trac.account_number like  CONCAT('%', {0}, '%') or trap.payoneer_email like CONCAT('%', {1}, '%')) ",
//            receiptAccount, receiptAccount);

        // 只考虑提现
        lqw.eq(TransactionReceipt::getTransactionType, TransactionTypeEnum.Withdrawal.getValue());
        lqw.orderByDesc(TransactionReceipt::getCreateTime);
        return lqw;
    }

    /**
     * 查询平台提现记录
     *
     * @param bo
     * @return
     */
    @Override
    public List<TransactionReceiptWithdrawalPlatformVo> queryPlatformWithdrawalRecord(WithdrawalRecordReviewQueryBo bo) {
        LambdaQueryWrapper<TransactionReceipt> lqw = builderPlatformReceiptWithdrawalLqw(bo);
        List<TransactionReceipt> records = TenantHelper.ignore(() -> transactionReceiptMapper.selectList(lqw));
        return convertWithdrawalPlatformVos(records);
    }

    /**
     * 实体转换
     *
     * @param records
     * @return
     */
    @NotNull
    private List<TransactionReceiptWithdrawalPlatformVo> convertWithdrawalPlatformVos(List<TransactionReceipt> records) {
        List<TransactionReceiptWithdrawalPlatformVo> withdrawalPlatformVos = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                TransactionReceiptWithdrawalPlatformVo vo = MapstructUtils.convert(record, TransactionReceiptWithdrawalPlatformVo.class);

                Long receiptAccountId = vo.getReceiptAccountId();
                if (receiptAccountId != null) {
                    String accountName = getAccountName(receiptAccountId, vo.getTransactionMethod());
                    vo.setAccountName(accountName);
                }

                withdrawalPlatformVos.add(vo);
            });
        }
        return withdrawalPlatformVos;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public R<Void> updateReceipt(WithdrawalRecordDetailVo bo) throws Exception {
        log.info("进入【更新提现信息】方法，{}", JSONUtil.toJsonStr(bo));

        String receiptNo = bo.getReceiptNo();
        String receiptStatus = bo.getReceiptStatus();
        String note = bo.getNote();
        ReceiptReviewStateEnum receiptReviewStatusType = ReceiptReviewStateEnum.valueOf(receiptStatus);
        List<WithdrawalRecordDetailVo.Attachment> reqAttachmentList = bo.getAttachmentList();

        LambdaQueryWrapper<TransactionReceipt> trLqw = new LambdaQueryWrapper<>();
        trLqw.eq(TransactionReceipt::getTransactionReceiptNo, receiptNo)
            .eq(TransactionReceipt::getTransactionType, TransactionTypeEnum.Withdrawal);

        TransactionReceipt paymentReceipt = TenantHelper.ignore(() -> transactionReceiptMapper.selectOne(trLqw));
        if (paymentReceipt == null) {
            return R.fail(ZSMallStatusCodeEnum.RECEIPT_RECORD_NOT_FOUND_ERROR);
        }
        //交易记录
        Long transactionsId = paymentReceipt.getTransactionsId();
        TransactionRecord transactions = TenantHelper.ignore(() -> transactionRecordMapper.selectById(transactionsId));
        String tenantId = transactions.getTenantId();

        LambdaQueryWrapper<TransactionsBill> tbLqw = new LambdaQueryWrapper<>();
        tbLqw.eq(TransactionsBill::getTransactionsId, transactionsId);
        List<TransactionsBill> transactionsBills = TenantHelper.ignore(() -> transactionsBillMapper.selectList(tbLqw));
        List<Long> billIds = transactionsBills.stream().map(TransactionsBill::getBillId).collect(Collectors.toList());

        //到账状态需要上传转账证明
        if (ObjectUtil.equals(receiptReviewStatusType, ReceiptReviewStateEnum.Accepted)) {
            if (CollUtil.isEmpty(reqAttachmentList)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }
            Date now = new Date();
            paymentReceipt.setReceiptTime(LocalDateTimeUtil.of(now));

            List<TransactionReceiptAttachment> attachmentEntityList = new ArrayList<>();
            long sort = 0;
            for (WithdrawalRecordDetailVo.Attachment reqAttachment : reqAttachmentList) {
                String savePath = reqAttachment.getSavePath();
                String showUrl = reqAttachment.getShowUrl();
                String fileName = reqAttachment.getFileName();
                String type = reqAttachment.getType();

                TransactionReceiptAttachment attachmentEntity = new TransactionReceiptAttachment();
                attachmentEntity.setTransactionReceiptNo(receiptNo);
                attachmentEntity.setOssId(reqAttachment.getOssId());
                attachmentEntity.setAttachmentName(fileName);
                attachmentEntity.setAttachmentSavePath(savePath);
                attachmentEntity.setAttachmentShowUrl(showUrl);
                attachmentEntity.setAttachmentSort(sort++);
                attachmentEntity.setAttachmentType(
                    (StrUtil.equals(type, "pdf") || StrUtil.equals(type, "PDF")) ? AttachmentTypeEnum.File.getValue() : AttachmentTypeEnum.Image.getValue()
                );
                attachmentEntityList.add(attachmentEntity);
            }
            transactionReceiptAttachmentMapper.insertBatch(attachmentEntityList);

            //修改账单提现状态为已提现
            if (CollUtil.size(billIds) > 0) {
                LambdaQueryWrapper<Bill> billLqw = new LambdaQueryWrapper<>();
                billLqw.in(Bill::getId, billIds);
                List<Bill> billList = TenantHelper.ignore(() -> billMapper.selectList(billLqw));

                billList.forEach(bill -> {
                    bill.setWithdrawalState(WithdrawalStateEnum.Withdrawal);
                });
                if (!TenantHelper.ignore(() -> billMapper.updateBatchById(billList))) {
                    throw new Exception("修改账单数据提现状态为已提现操作失败！");
                }
            }
            //更新交易记录
            transactions.setTransactionTime(now);
            transactions.setTransactionState(TransactionStateEnum.Success);
            TenantHelper.ignore(() -> transactionRecordMapper.updateById(transactions));
        } else if (ObjectUtil.equals(receiptReviewStatusType, ReceiptReviewStateEnum.Rejected)) {
            //还原账单提现状态为未提现
            if (CollUtil.size(billIds) > 0) {
                LambdaQueryWrapper<Bill> billLqw = new LambdaQueryWrapper<>();
                billLqw.in(Bill::getId, billIds);
                List<Bill> billList = TenantHelper.ignore(() -> billMapper.selectList(billLqw));
                billList.forEach(bill -> {
                    bill.setWithdrawalState(WithdrawalStateEnum.NotWithdrawal);
                });
                if (!TenantHelper.ignore(() -> billMapper.updateBatchById(billList))) {
                    throw new Exception("还原账单数据提现状态为未提现操作失败！");
                }
            }
            //更新交易记录
            transactions.setTransactionState(TransactionStateEnum.Failure);
            TenantHelper.ignore(() -> transactionRecordMapper.updateById(transactions));
        }
        paymentReceipt.setReviewState(receiptReviewStatusType);
        paymentReceipt.setNoteManager(note);
        TenantHelper.ignore(() -> transactionReceiptMapper.updateById(paymentReceipt));

        return R.ok();
    }

    @Override
    public WithdrawalAccountVo getAccountList(TenantReceiptAccountBo bo) {
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<TenantReceiptAccount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantReceiptAccount::getTenantId, tenantId)
            .eq(TenantReceiptAccount::getDelFlag, "0")
            .eq(TenantReceiptAccount::getAccountStatus, 1);
        List<TenantReceiptAccount> tenantReceiptAccounts = TenantHelper.ignore(() -> tenantReceiptAccountMapper.selectList(lqw));

        WithdrawalAccountVo withdrawalAccount = new WithdrawalAccountVo();
        List<TenantReceiptAccountVo> payoneerList = new ArrayList<>();
        List<TenantReceiptAccountVo> creditList = new ArrayList<>();

        if (CollUtil.isNotEmpty(tenantReceiptAccounts)) {
            tenantReceiptAccounts.forEach(bean -> {
                TenantReceiptAccountVo vo = MapstructUtils.convert(bean, TenantReceiptAccountVo.class);

                Long id = bean.getId();
                ReceiptAccountTypeEnum accountTypeEnum = bean.getAccountType();
                // 不同卡类型，查询内容不一样
                if (StrUtil.equals(accountTypeEnum.getValue(), ReceiptAccountTypeEnum.Credit.getValue())) {
                    // 银行卡
                    TenantReceiptAccountCreditVo tenantReceiptAccountCreditVo = iTenantReceiptAccountService.getReceiptAccountCreditVo(id);
                    vo.setReceiptAccountCredit(tenantReceiptAccountCreditVo);
                    // 附件
                    List<TenantReceiptAccountAttachmentVo> attachmentVos = iTenantReceiptAccountService.getReceiptAccountAttachmentVos(id);
                    vo.setAttachmentList(attachmentVos);

                    creditList.add(vo);
                }
                if (StrUtil.equals(accountTypeEnum.getValue(), ReceiptAccountTypeEnum.Payoneer.getValue())) {
                    TenantReceiptAccountPayoneerVo tenantReceiptAccountPayoneerVo = iTenantReceiptAccountService.getReceiptAccountPayoneerVo(id);
                    vo.setReceiptAccountPayoneer(tenantReceiptAccountPayoneerVo);
                    payoneerList.add(vo);
                }
            });
        }
        withdrawalAccount.setCreditList(creditList);
        withdrawalAccount.setPayoneerList(payoneerList);
        return withdrawalAccount;
    }


}
