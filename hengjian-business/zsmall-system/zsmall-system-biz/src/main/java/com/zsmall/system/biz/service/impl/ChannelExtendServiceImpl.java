package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.product.entity.domain.bo.channelExtend.RakutenApiStatusCheckBo;
import com.zsmall.product.entity.domain.vo.channelExtend.RakutenApiStatusCheckVo;
import com.zsmall.product.entity.domain.vo.channelExtend.RakutenGenreVo;
import com.zsmall.system.biz.service.ChannelExtendService;
import com.zsmall.system.entity.domain.ChannelExtendRakutenApiStatus;
import com.zsmall.system.entity.domain.ChannelExtendRakutenGenre;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.IChannelExtendRakutenApiStatusService;
import com.zsmall.system.entity.iservice.IChannelExtendRakutenGenreService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 渠道扩展功能-业务实现层
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelExtendServiceImpl implements ChannelExtendService {

    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IChannelExtendRakutenGenreService iChannelExtendRakutenGenreService;
    private final IChannelExtendRakutenApiStatusService iChannelExtendRakutenApiStatusService;

    /**
     * 查询乐天品类树（提供给下拉选使用）
     * @param parentId
     * @return
     */
    @Override
    public List<RakutenGenreVo> queryRakutenGenreForSelect(String parentId) {
        LambdaQueryWrapper<ChannelExtendRakutenGenre> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(parentId)) {
            lqw.eq(ChannelExtendRakutenGenre::getParentGenreId, parentId);
        } else {
            lqw.eq(ChannelExtendRakutenGenre::getParentGenreId, "0");
        }
        lqw.orderByAsc(ChannelExtendRakutenGenre::getId);

        List<ChannelExtendRakutenGenre> categoryList = iChannelExtendRakutenGenreService.list(lqw);
        List<RakutenGenreVo> categorySelectVos = BeanUtil.copyToList(categoryList, RakutenGenreVo.class);
        for (RakutenGenreVo categorySelectVo : categorySelectVos) {
            categorySelectVo.setLeaf(!iChannelExtendRakutenGenreService.existsByParentId(categorySelectVo.getId()));
        }
        return categorySelectVos;

    }

    /**
     * 乐天Api状态检查
     *
     * @param bo
     * @return
     */
    @Override
    public RakutenApiStatusCheckVo rakutenApiStatusCheck(RakutenApiStatusCheckBo bo) {
        LoginHelper.getLoginUser(TenantType.Distributor);
        Long channelId = bo.getChannelId();

        TenantSalesChannel salesChannel = iTenantSalesChannelService.queryByIdAndChannelType(channelId, ChannelTypeEnum.Rakuten);
        if (salesChannel != null) {
            ChannelExtendRakutenApiStatus rakutenApiStatus = iChannelExtendRakutenApiStatusService.queryByChannelId(channelId);
            if (rakutenApiStatus == null) {
                rakutenApiStatus = new ChannelExtendRakutenApiStatus();
                rakutenApiStatus.setSalesChannelId(channelId);
            }



        }

        return null;
    }
}
