package com.zsmall.system.entity.domain.bo.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年3月7日  14:27
 * @description:
 */
@Data
public class AirwallexOrderPayBo {

    /**
     * 订单编号集合
     */
    private List<String> orderNoList;

    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 派安盈创建支付请求返回地址
     */
    private String returnUrl;

    /**
     * 派安盈创建支付请求取消地址
     */
    private String cancelUrl;

    /**
     * 支付类型 1：空中云汇 2：派安盈
     */
    private Integer type;

    /**
     * 派安盈账户id
     */
    private Long payoneerAccountId;
}
