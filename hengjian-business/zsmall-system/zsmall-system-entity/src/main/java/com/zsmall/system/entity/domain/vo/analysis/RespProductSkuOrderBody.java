package com.zsmall.system.entity.domain.vo.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品sku订单通用响应参数
 *
 * <AUTHOR>
 * @date 2022/9/21 17:00
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "商品sku订单通用响应参数")
public class RespProductSkuOrderBody extends StatisticsProductSkuBaseVo {

    @Schema(title = "itemNo")
    private String itemNo;

    @Schema(title = "产品图片url")
    private String showUrl;

    @Schema(title = "SKU")
    private String sku;

    @Schema(title = "供货商ID")
    private String tenantId;

    @Schema(title = "公司名字")
    private String companyName;

    @Schema(title = "公司邮箱")
    private String email;

    @Schema(title = "公司电话")
    private String phoneNumber;

    @Schema(title = "上架时间")
    private String createTime;

    @Schema(title = "更新时间")
    private String updateTime;

    @Schema(title = "代发价格")
    private BigDecimal dropShippingPrice;

    @Schema(title = "自提价格")
    private BigDecimal pickUpPrice;

    @Schema(title = "库存数")
    private Long stockTotal;

    @Schema(title = "sku总数")
    private Integer skuNum;

    @Schema(title = "有库存的SKU总数")
    private Integer skuInventoryNum;

    @Schema(title = "铺货的SKU总数")
    private Integer skuDistributionNum;

    @Schema(title = "有出单的SKU总数")
    private Integer skuOrderNum;

    @Schema(title = "销售件数总数")
    private Integer salesNum;

    @Schema(title = "订单总数")
    private Integer orderTotalNum;

    @Schema(title = "出单总金额")
    private BigDecimal orderTotalPrice;

    @Schema(title = "售后订单总数")
    private Integer restockTotalNum;

    @Schema(title = "被收藏数（分销商数）")
    private Integer dropNumByDis;

    @Schema(title = "订单处理时效")
    private Long orderDealEffectiveness;

    @Schema(title = "被下载此处（分销商）")
    private Integer downloadedByDis;

    @Schema(title = "被点击的产品sku（按照停留时长排名）")
    private Integer clickedSku;


}
