package com.zsmall.system.entity.domain.bo.extraSetting;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.system.entity.domain.TenantPayoneer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 支付账户管理业务对象 tenant_payoneer
 *
 * <AUTHOR> Li
 * @date 2023-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantPayoneer.class, reverseConvertGenerate = false)
public class TenantPayoneerBo extends NoDeptTenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * payoneer账户别名
     */
    @NotBlank(message = "payoneer账户别名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountName;

    /**
     * Payoneer平台Id
     */
    @NotBlank(message = "Payoneer平台Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountId;

    /**
     * 是否默认
     */
    @NotNull(message = "是否默认不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isDefault;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
