package com.zsmall.system.entity.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 账单表
 *
 * @TableName bill
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class BillAdminExport {
    @ExcelIgnore
    private Long id;
    /**
     * 供货商
     */
    @ExcelProperty("供货商")
    private String tenantId;
    /**
     * 账单编号
     */
    @ExcelProperty("账单编号")
    private String billNo;


    /**
     * 结算周期
     */
    @ExcelProperty("结算周期")
    private String settlementDate;


    /**
     * 本期收入
     */
    @ExcelProperty("本期收入")
    private BigDecimal currentIncome;

    /**
     * 本期支出
     */
    @ExcelProperty("本期支出")
    private BigDecimal currentExpenditure;

    /**
     * 本期循环保证金
     */
    @ExcelProperty("本期循环保证金")
    private BigDecimal currentCircularDeposit;

    /**
     * 上期循环保证金
     */
    @ExcelProperty("上期循环保证金")
    private BigDecimal previousCircularDeposit;


    /**
     * 本期总金额
     */
    @ExcelProperty("本期总金额")
    private BigDecimal currentTotalAmount;

    /**
     * 账单状态（0-未结算，1-已结算）
     */
    @ExcelProperty("账单状态")
    private String billState;


}
