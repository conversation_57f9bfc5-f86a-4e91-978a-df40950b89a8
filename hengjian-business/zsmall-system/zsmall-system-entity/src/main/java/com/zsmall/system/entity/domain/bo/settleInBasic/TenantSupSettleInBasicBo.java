package com.zsmall.system.entity.domain.bo.settleInBasic;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.TenantSupSettleInBasic;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 供应商入驻基本信息业务对象 tenant_sup_settle_in_basic
 *
 * <AUTHOR> Li
 * @date 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantSupSettleInBasic.class, reverseConvertGenerate = false)
public class TenantSupSettleInBasicBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户编码
     */
    @NotBlank(message = "用户编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String creatorCode;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String companyName;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String socialCreditCode;

    /**
     * 国家id
     */
    @NotNull(message = "国家id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long countryId;

    /**
     * 省/州id
     */
    @NotNull(message = "省/州id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long stateId;

    /**
     * 省/州文本
     */
    @NotBlank(message = "省/州文本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String stateText;

    /**
     * 城市id
     */
    @NotNull(message = "城市id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long cityId;

    /**
     * 城市文本
     */
    @NotBlank(message = "城市文本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cityText;

    /**
     * 注册地址
     */
    @NotBlank(message = "注册地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String registeredAddress;

    /**
     * 法人归属地
     */
    @NotBlank(message = "法人归属地不能为空", groups = {AddGroup.class, EditGroup.class})
    private String legalPersonPlace;

    /**
     * 证件类型
     */
    @NotNull(message = "证件类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long documentType;

    /**
     * 证件号
     */
    @NotBlank(message = "证件号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String documentNumber;

    /**
     * 法定代表人姓名
     */
    @NotBlank(message = "法定代表人姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String legalPersonName;


}
