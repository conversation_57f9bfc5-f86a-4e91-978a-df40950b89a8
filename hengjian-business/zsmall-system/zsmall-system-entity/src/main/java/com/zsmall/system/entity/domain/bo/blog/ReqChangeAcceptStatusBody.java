package com.zsmall.system.entity.domain.bo.blog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-更改受理状态
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-更改受理状态")
public class ReqChangeAcceptStatusBody {

    @Schema(title = "受理的反馈id")
    private Long id;

    @Schema(title = "受理内容")
    private String content;
}
