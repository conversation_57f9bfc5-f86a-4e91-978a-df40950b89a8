package com.zsmall.system.entity.domain.bo.marketplaceConfig;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 请求体-保存商城配置
 *
 * <AUTHOR>
 * @date 2023/9/6
 */
@Data
public class MarketplaceConfigSaveBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置集合
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private List<MarketplaceConfigDetailBo> marketplaceConfigList;

}
