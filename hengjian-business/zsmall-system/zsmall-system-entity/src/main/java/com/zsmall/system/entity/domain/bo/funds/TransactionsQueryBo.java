package com.zsmall.system.entity.domain.bo.funds;

import lombok.Data;

import java.util.List;

/**
 * 交易记录
 */
@Data
public class TransactionsQueryBo {

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单退款单号
     */
    private String orderRefundNo;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 交易子类型
     */
    private String transactionSubType;

    /**
     * 交易状态
     */
    private String transactionState;

    /**
     * 租户Id
     */
    private String tenantId;

    /**
     * 租户类型
     */
    private String tenantType;

    /**
     * 创建时间 - 查询时间范围
     */
    private List<String> searchDates;

    private String createTimeStart;
    private String createTimeEnd;

    /**
     * 币种
     */
    private String currency;
}
