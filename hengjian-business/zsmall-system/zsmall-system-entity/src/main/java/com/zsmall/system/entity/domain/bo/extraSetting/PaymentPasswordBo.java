package com.zsmall.system.entity.domain.bo.extraSetting;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
public class PaymentPasswordBo {

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    private String newPassword;
    /**
     * 确认新密码
     */
    @NotBlank(message = "确认新密码不能为空")
    private String confirmPassword;

}
