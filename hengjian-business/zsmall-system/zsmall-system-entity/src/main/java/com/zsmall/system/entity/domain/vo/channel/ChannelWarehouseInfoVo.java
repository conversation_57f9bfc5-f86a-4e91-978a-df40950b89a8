package com.zsmall.system.entity.domain.vo.channel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;



/**
 * 渠道仓库关联信息视图对象 channel_warehouse_info
 *
 * <AUTHOR> Li
 * @date 2024-11-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ChannelWarehouseInfo.class)
public class ChannelWarehouseInfoVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户渠道表id
     */
    @ExcelProperty(value = "租户渠道表id")
    private Long tenantSaleChannelId;

    /**
     * 管理仓库id
     */
    @ExcelProperty(value = "管理仓库id")
    private Long warehouseAdminId;

    /**
     * 仓库code
     */
    @ExcelProperty(value = "仓库code")
    private String warehouseCode;

    /**
     * 渠道名称
     */
    @ExcelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 店铺仓库名称
     */
    @ExcelProperty(value = "店铺仓库名称")
    private String channelWarehouseName;

    /**
     * 店铺仓库标识符
     */
    @ExcelProperty(value = "店铺仓库标识符")
    private String channelWarehouseCode;


}
