package com.zsmall.system.entity.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.hengjian.common.core.utils.JacksonUtils;
import com.zsmall.common.enums.SalesChannelInterfaceStatusEnum;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.mapper.TenantSalesChannelMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/19 10:51
 */
@Slf4j
@Component
@Lazy
public class AuzTiktokUtil {

    @Resource
    private TenantSalesChannelMapper mapper;

    /**
     * 功能描述：获取抖音商店信息
     *
     * @param appKey             应用程序密钥
     * @param appSecret          应用程序机密
     * @param accessToken        访问令牌
     * @param tenantSalesChannel 租户销售渠道
     * <AUTHOR>
     * @date 2024/03/19
     */
    public void getTikTokShopInfo(String appKey, String appSecret, String accessToken, TenantSalesChannel tenantSalesChannel) {
        try {
            String path = "/authorization/202309/shops";
            String url = "https://open-api.tiktokglobalshop.com" + path;
            Long timestamp = System.currentTimeMillis() / 1000;

            String sign = getTikTokSignV2(appKey, appSecret, timestamp, path, null, null);
            String param = "?timestamp=" + timestamp + "&sign=" + sign + "&app_key=" + appKey;
            HttpResponse httpResponse = HttpRequest.get(url + param)
                                                   .header(Header.ACCEPT_CHARSET, "UTF-8")
                                                   .header("content-type", "application/json")
                                                   .header("x-tts-access-token", accessToken)
                                                   .body("")
                                                   .timeout(20000)//超时，毫秒
                                                   .execute();

            String body = httpResponse.body();
            log.info("请求tiktok的返回值body为:{}", body);
//            String body="{\n" +
//                "  \"code\": 0,\n" +
//                "  \"data\": {\n" +
//                "    \"shops\": [\n" +
//                "      {\n" +
//                "        \"cipher\": \"GCP_XF90igAAAABh00qsWgtvOiGFNqyubMt3\",\n" +
//                "        \"code\": \"CNGBCBA4LLU8\",\n" +
//                "        \"id\": \"7000714532876273420\",\n" +
//                "        \"name\": \"Maomao beauty shop\",\n" +
//                "        \"region\": \"GB\",\n" +
//                "        \"seller_type\": \"CROSS_BORDER\"\n" +
//                "      }\n" +
//                "    ]\n" +
//                "  },\n" +
//                "  \"message\": \"Success\",\n" +
//                "  \"request_id\": \"202203070749000101890810281E8C70B7\"\n" +
//                "}";
            Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
            HashMap<String, ArrayList> data = (HashMap) responseMap.get("data");

            ArrayList arrayList = data.get("shops");
            HashMap shopMap = (HashMap) arrayList.get(0);

            String connectStr = tenantSalesChannel.getConnectStr();
            Map<String, Object> connectMap = null;
            if(StrUtil.isEmpty(connectStr)){
                connectMap = new HashMap<>();
            }else{
                connectMap = JacksonUtils.jsonToMap(connectStr);
            }

            connectMap.put("shopId", shopMap.get("id"));
            connectMap.put("shopCipher", shopMap.get("cipher"));
            String newConnectStr = JacksonUtils.toJson(connectMap);
            tenantSalesChannel.setConnectStr(newConnectStr);
            tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.NORMAL.name());
            mapper.insertOrUpdate(tenantSalesChannel);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取tiktok店铺信息异常：{}", e.getMessage());
        }
    }
    public String getTikTokSignV2(String appKey, String appSecret, Long timestamp, String path, Map<String, String> param, String reqBody) {
        String sign = "";
        String unSign = appSecret + path + "app_key" + appKey;
        if (null != param) {
            for (Map.Entry<String, String> entry : param.entrySet()) {
                unSign = unSign + entry.getKey() + entry.getValue();
            }
        }
        unSign = unSign + "timestamp" + timestamp;
        if (StringUtils.isNotEmpty(reqBody)) {
            unSign = unSign + reqBody;
        }
        unSign = unSign + appSecret;
        log.info("tiktok V2版本接口待签名字符串#{}", unSign);
        sign = hmacSha256Encrypt(unSign, appSecret);
        log.info("tiktok V2版本接口签名字符串#{}", sign);
        return sign;
    }

    public String hmacSha256Encrypt(String message, String secretKey) {
        try {
            // 创建一个HMAC-SHA256算法实例，并指定密钥
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSha256.init(secretKeySpec);

            // 计算消息的摘要值
            byte[] hash = hmacSha256.doFinal(message.getBytes(StandardCharsets.UTF_8));

            return byte2Hex(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return null;
    }

    public String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }
}
