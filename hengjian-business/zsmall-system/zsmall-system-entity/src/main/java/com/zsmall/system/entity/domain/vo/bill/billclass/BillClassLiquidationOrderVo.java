package com.zsmall.system.entity.domain.vo.bill.billclass;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 响应信息-账单关系分类-清货订单信息
 *
 * <AUTHOR>
 * @date 2022/11/30
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BillClassLiquidationOrderVo extends BillClassDetailVo {

    /**
     * 清货编号
     */
    private String liquidationCode;

    /**
     * 清货订单号
     */
    private String liquidationOrderNo;

    /**
     * 商品总价
     */
    private String productAmount;

    /**
     * 操作费总价
     */
    private String operationFee;

    /**
     * 尾程派送费总价
     */
    private String finalDeliveryFee;

    /**
     * 清货托盘费
     */
    private String liquidationPalletFee;

}
