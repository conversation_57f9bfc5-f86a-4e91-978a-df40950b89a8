package com.zsmall.system.entity.domain.vo.bill.billclass;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 响应信息-账单关系分类-活动仓储费信息
 *
 * <AUTHOR>
 * @date 2022/11/30
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BillClassActivityStorageFeeVo extends BillClassDetailVo {

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 活动类型
     */
    private String activityType;

}
