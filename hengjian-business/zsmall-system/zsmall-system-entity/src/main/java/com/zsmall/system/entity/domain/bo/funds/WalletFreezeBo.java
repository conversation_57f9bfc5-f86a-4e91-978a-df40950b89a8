package com.zsmall.system.entity.domain.bo.funds;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 钱包冻结/解冻
 */
@Data
public class WalletFreezeBo {
    /**
     * 租户Id
     */
    @NotBlank(message = "{tenant.number.not.blank}")
    private String tenantId;
    /**
     * 钱包状态
     */
    @NotNull(message = "{zsmall.validated.notnull.walletState}")
    private Integer walletState;

    private String currency;

}
