package com.zsmall.system.entity.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class BillRelationDetailDto {
    @ExcelProperty("账单编号")
    private String billNo;
    @ExcelProperty("订单号")
    private String OrderNo;
    @ExcelProperty("交易类型")
    private String OrderType;
    @ExcelProperty("下单时间")
    private String CreateDateTime;
    @ExcelProperty("商品编码")
    private String ItemNo;
    @ExcelProperty("数量")
    private String ProductQuantity;
    @ExcelProperty("产品金额")
    private String ProductAmount;
    @ExcelProperty("操作费")
    private String OperationFee;
    @ExcelProperty("尾程派送费")
    private String FinalDeliveryFee;
    @ExcelProperty("金额")
    private String TotalAmount;

//    @ExcelProperty("退款单-订单号")
//    private String OrderRefundOrderNo;
//    @ExcelProperty("退款单-下单时间")
//    private String OrderRefundCreateDateTime;
//    @ExcelProperty("退款单-商品编码")
//    private String OrderRefundItemNo;
//    @ExcelProperty("退款单-金额")
//    private String OrderRefundTotalAmount;

}
