package com.zsmall.system.entity.domain.vo.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品分销商统计数据详情响应体
 *
 * <AUTHOR>
 * @date 2022/10/10 14:39
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "商品分销商统计数据详情响应体")
public class RespAnalysisProductSkuBulkBody extends StatisticsProductSkuBaseVo {


    @Schema(title = "日期集合")
    private List<String> dateList;

    @Schema(title = "每日铺货的SKU数集合")
    private List<Integer> skuDistributionNumList;

    @Schema(title = "每日出单的SKU数集合")
    private List<Integer> skuOrderNumList;

    @Schema(title = "每日销量集合")
    private List<Integer> salesNumList;

    @Schema(title = "每日代发订单数集合")
    private List<Integer> dropShippingOrderNumList;

    @Schema(title = "每日自提订单数集合")
    private List<Integer> pickUpOrderNumList;

    @Schema(title = "每日代发总金额集合")
    private List<BigDecimal> dropShippingTotalPriceList;

    @Schema(title = "每日自提总金额集合")
    private List<BigDecimal> pickUpTotalPriceList;

    @Schema(title = "不同渠道订单占比")
    private List<String> orderPercentList;

    @Schema(title = "不同渠道销量占比")
    private List<String> salesNumPercentList;

    @Schema(title = "渠道类型")
    private List<String> channelNameList;

    @Schema(title = "不同渠道的订单数")
    private List<Map<String, Object>> orderNumByChannel;

    @Schema(title = "不同渠道的订单总价")
    private List<Map<String, Object>> orderPriceByChannel;

    @Schema(title = "订单总数")
    private Integer orderTotalNum;

    @Schema(title = "销量")
    private Integer salesTotalNum;

    @Schema(title = "sku列表")
    private List<?> skuList;

    @Schema(title = "供应商列表")
    private List<?> supList;

    @Schema(title = "时间数组")
    private String[] searchDates;

}
