package com.zsmall.system.controller;

import com.hengjian.common.core.domain.R;
import com.zsmall.common.constant.ValidationMessage;
import com.zsmall.system.biz.service.BackgroundHomeService;
import com.zsmall.system.entity.domain.vo.backgroundHome.BackgroundHomeInfoVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * 后台Home相关功能
 *
 * <AUTHOR>
 * @date 2023/8/28
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/backgroundHome")
public class BackgroundHomeController {

    private final BackgroundHomeService backgroundHomeService;

    /**
     * 查询后台Home信息
     */
    @GetMapping("/queryBackgroundHomeInfo")
    public R<BackgroundHomeInfoVo> queryBackgroundHomeInfo() {
        return backgroundHomeService.queryBackgroundHomeInfo();
    }

    /**
     * 查询静态文章
     */
    @GetMapping("/queryStaticArticles")
    public R<String> queryStaticArticles(@NotBlank(message = ValidationMessage.API_REQUIRED)
                                         String type) {
        return backgroundHomeService.queryStaticArticles(type);
    }

}
