package com.zsmall.extend.shop.cache;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.core.cache.ExAccessTokenCache;
import com.zsmall.extend.shop.constants.CacheConstants;
import com.zsmall.extend.shop.service.business.TpsBusinessShopifyService;
import com.zsmall.extend.shop.shopify.model.accesstoken.AccessToken;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

/**
 * Shopify AccessToken Cache Redis实现
 */
@RequiredArgsConstructor
public class ExRedisShopifyAccessTokenCache implements ExAccessTokenCache {

    private final String EX_CACHE_DEFAULT = "SHOP:SHOPIFY:";


    @Override
    public String get(String key) {
        String cacheObject = RedisUtils.getCacheObject(getKey(key));
        if(StrUtil.isBlank(cacheObject)) {
            // 如果缓存中不存在，则从数据库中获取
            TpsBusinessShopifyService tpsBusinessShopifyService = SpringUtils.getBean(TpsBusinessShopifyService.class);
            AccessToken accessTokenFromDb = tpsBusinessShopifyService.getAccessTokenFromDb(key);
            if(accessTokenFromDb != null) {
                // 如果数据库中有值，则直接转成对应内容，并设置缓存
                cacheObject = JSONUtil.toJsonStr(accessTokenFromDb);
                this.set(key, cacheObject);
            }
        }

        return cacheObject;
    }

    @Override
    public void set(String key, String jsonValue) {
        RedisUtils.setCacheObject(getKey(key), jsonValue, Duration.ofDays(CacheConstants.CACHE_DEFAULT_EXPIRATION));
    }

    @Override
    public void remove(String key) {
        RedisUtils.deleteObject(getKey(key));
    }

    private String getKey(String key) {
        return GlobalConstants.GLOBAL_REDIS_KEY + EX_CACHE_DEFAULT + key;
    }
}
