package com.zsmall.extend.shop.config;

import com.zsmall.extend.shop.cache.ExRedisWayfairAccessTokenCache;
import com.zsmall.extend.shop.cache.ExRedisWayfairConfigCache;
import com.zsmall.extend.wayfair.kit.AccessTokenKit;
import com.zsmall.extend.wayfair.kit.WayfairKit;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnProperty(value = "thirdparty-shop.wayfair.enabled", havingValue = "true")
public class WayfairConfig {

    @Bean
    public void setWayfairAccessTokenCache() {
        // 设置redis缓存
        AccessTokenKit.setCache(new ExRedisWayfairAccessTokenCache());

        WayfairKit.setCache(new ExRedisWayfairConfigCache());
    }

}
