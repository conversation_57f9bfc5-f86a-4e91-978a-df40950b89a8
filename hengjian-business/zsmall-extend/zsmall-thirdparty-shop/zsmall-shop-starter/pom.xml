<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.shop</groupId>
        <artifactId>zsmall-thirdparty-shop</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <groupId>com.zsmall.extend.shop</groupId>
    <artifactId>zsmall-shop-starter</artifactId>
    <name>ZS-Mall第三方店铺模块 starter</name>

    <description>
        ZS-Mall第三方店铺模块 starter
    </description>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-satoken</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-shopify</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-wayfair</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-rakuten</artifactId>
        </dependency>

        <!-- amazon 模块 -->
        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-amazon-kit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-amazon-business-controller</artifactId>
        </dependency>


    </dependencies>

</project>
