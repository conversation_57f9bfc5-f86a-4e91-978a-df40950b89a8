package com.zsmall.extend.wayfair.model.order;

import com.zsmall.extend.wayfair.enums.TransactionState;
import lombok.Data;

/**
 * 输出船舶通知变更
 */
@Data
public class OutShipNotificationMutation {

    /**
     * The Ship Notification is an asynchronous operation.
     * The transaction data returned to you can be used to track the progress of your mutation.
     */
    private TransactionState transactionState;

}
