package com.zsmall.extend.wayfair.model.product;

import com.zsmall.extend.wayfair.enums.TransactionState;
import lombok.Data;

/**
 *
 */
@Data
public class OutProductUpdateTransactionStatus {

    /**
     * The identifier string for the product update transaction submitted.
     */
    private String handle;

    /**
     * The field that represents one of four states of a transaction (NEW, PROCESSING, ERROR, COMPLETE).
     */
    private TransactionState status;

    /**
     * The data object containing product update status information at the product or supplier part level and the
     * property or field level.
     */
    private OutSupplierPartUpdateTransactionStatus productTransactions;

    /**
     * Any information the user must know regarding the product update status query submitted, such as validation
     * errors or internal server errors.
     */
    private String message;

}
