package com.zsmall.extend.wayfair.model.product;

import com.zsmall.extend.wayfair.enums.Country;
import com.zsmall.extend.wayfair.enums.ShipClass;
import com.zsmall.extend.wayfair.model.common.Dimension;
import com.zsmall.extend.wayfair.model.common.Weight;
import lombok.Data;

import java.util.List;

/**
 * 项目产品
 */
@Data
public class ProjectProduct {

    /**
     * The part number provided by the supplier for this item.
     */
    private String supplierPartNumber;

    /**
     * The part number that the manufacturer has given this part.
     */
    private String manufacturerPartNumber;

    /**
     * The name of the product.
     */
    private String name;

    /**
     * The country this product was manufactured in.
     */
    private Country countryOfOrigin;

    /**
     * The default ship class of the product.
     */
    private ShipClass shipVia;

    /**
     * The length of the product
     */
    private Dimension length;

    /**
     * The width of the product
     */
    private Dimension width;

    /**
     * The height of the product
     */
    private Dimension height;

    /**
     * The weight of the product
     */
    private Weight weight;

    /**
     * The manufacturer of the product
     */
    private Manufacturer manufacturer;

    /**
     * The category that this product belongs to
     */
    private OutProductCategory category;

    /**
     * The validation error that occurred when trying
     */
    private List<CatalogValidationError> validationErrors;

}
