package com.zsmall.extend.wayfair.model.shipping;

import com.zsmall.extend.wayfair.model.common.ShippingLabel;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * This type includes all the information that resulted from the generation of labels for a purchase order.
 */
@Data
public class LabelGenerationEvent {

    /**
     * The identifier of the label generation event
     */
    private Long id;

    /**
     * The date time that this event happened
     */
    private ZonedDateTime eventDate;

    /**
     * This is the consolidated shipping label for this purchase order. It contains all the labels for the order.
     */
    private ShippingLabel consolidatedShippingLabel;

    /**
     * Information about generated shipping labels in this event
     */
    private List<ShippingLabelInfo> generatedShippingLabels;

    /**
     * Information about a specific customs document
     */
    private CustomsDocumentType customsDocument;

}
