package com.zsmall.extend.wayfair.model.product;

import com.zsmall.extend.wayfair.enums.TransactionState;
import lombok.Data;

/**
 *
 */
@Data
public class OutSupplierPartUpdateTransactionStatus {

    /**
     * The identifier string for the product update transaction submitted.
     */
    private String handle;

    /**
     * The field that represents one of four states of a transaction (NEW, PROCESSING, ERROR, COMPLETE).
     */
    private TransactionState status;

    /**
     * The supplier part number of the product whose status is being returned.
     */
    private String supplierPartNumber;

    /**
     * The data object containing product update status information at the property or field level.
     */
    private OutProductAttributeUpdateTransactionStatus properties;

}
