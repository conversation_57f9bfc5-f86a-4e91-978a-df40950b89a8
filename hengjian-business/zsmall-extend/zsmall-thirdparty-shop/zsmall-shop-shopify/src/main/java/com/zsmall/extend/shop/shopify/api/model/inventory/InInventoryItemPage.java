package com.zsmall.extend.shop.shopify.api.model.inventory;

import com.zsmall.extend.shop.shopify.api.model.common.InPageable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @date 2022-12-25 10:22
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class InInventoryItemPage extends InPageable {

    /**
     * Show only inventory items specified by a comma-separated list of IDs.
     * (maximum: 100)
     */
    private String ids;

}
