package com.zsmall.extend.shop.shopify.api.admin;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.shop.shopify.api.model.common.InPageable;
import com.zsmall.extend.shop.shopify.api.model.common.OutCommonBean;
import com.zsmall.extend.shop.shopify.enums.ShopifyErrorEnums;
import com.zsmall.extend.shop.shopify.exception.ShopifyClientException;
import com.zsmall.extend.shop.shopify.model.ShopifyClientBean;
import com.zsmall.extend.shop.shopify.model.ShopifyPage;
import com.zsmall.extend.shop.shopify.model.inventory.InventoryLevelList;
import com.zsmall.extend.shop.shopify.model.inventory.Location;
import com.zsmall.extend.shop.shopify.model.inventory.LocationList;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商店位置接口
 * 位置
 * 位置表示您的商店，弹出式商店，总部和仓库所在的地理位置。您可以使用“位置”资源来跟踪销售，管理库存以及配置要在结帐时应用的税率。
 * <p>
 * Location
 * A location represents a geographical location where your stores, pop-up stores, headquarters, and warehouses exist
 * . You can use the Location resource to track sales, manage inventory, and configure the tax rates to apply at
 * checkout.
 */
@Slf4j
public class LocationApi extends Component {

    /**
     * GET - Retrieves a list of locations
     */
    private static final String URL_LOCATION = "/admin/api/{version}/locations.json";
    /**
     * GET - Retrieves a single location by its ID
     */
    private static final String URL_LOCATION_ID = "/admin/api/{version}/locations/{location_id}.json";
    /**
     * GET - Retrieves a count of locations
     */
    private static final String URL_LOCATION_COUNT = "/admin/api/{version}/locations/count.json";
    /**
     * GET - Retrieves a list of inventory levels for a location.
     */
    private static final String URL_LOCATION_ID_LEVELS = "/admin/api/{version}/locations/{location_id}/inventory_levels"
        + ".json";


    public LocationApi(ShopifyClientBean shopifyClientBean, String shopDomain) {
        super(shopifyClientBean, shopDomain);
    }

    /**
     * 检索位置列表
     *
     * @return
     */
    public List<Location> getLocations() {
        log.info("LocationApi - getLocations url = {}", URL_LOCATION);

        LocationList locationList =
            this.httpResponseWithAccessToken(getRequestUrl(URL_LOCATION), Method.GET, null, LocationList.class);
        if (locationList == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
        return locationList.getLocations();
    }

    /**
     * 通过其ID检索单个位置
     *
     * @return
     */
    private Location getLocation(Long locationId) {
        Assert.notNull(locationId, "[Assertion failed] - {} it must not be null. ", "locationId");
        String url = this.replaceUrlValue(getRequestUrl(URL_LOCATION_ID), "location_id", locationId);
        log.info("LocationApi - getLocation url = {}", url);

        OutCommonBean outCommonBean =
            this.httpResponseWithAccessToken(url, Method.GET, null, OutCommonBean.class);
        if (outCommonBean == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
        return outCommonBean.getLocation();
    }


    /**
     * 检索位置数。
     *
     * @return
     */
    public Long countLocation() {
        log.info("LocationApi - countLocation url = {}", URL_LOCATION_COUNT);
        OutCommonBean outCommonBean =
            this.httpResponseWithAccessToken(getRequestUrl(URL_LOCATION_COUNT), Method.GET, null, OutCommonBean.class);
        if (outCommonBean == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
        return outCommonBean.getCount();
    }

    /**
     * 首次查询-需自定义参数
     * 检索位置的库存水平列表。注意：从2019-10版本开始，此端点通过使用响应标头中提供的链接来实现分页。发送page参数将返回错误。要了解更多信息，请参阅向分页的REST Admin API端点发出请求。
     *
     * @param locationId
     * @param pageable
     * @return
     */
    public ShopifyPage<InventoryLevelList> getLocationInventoryLevels(Long locationId, InPageable pageable) {
        this.checkPageParameters(pageable);
        Assert.notNull(locationId, "[Assertion failed] - {} it must not be null. ", "locationId");

        Map<String, Object> paramMap = this.getStringObjectMap(pageable);
        log.info("LocationApi - getLocationInventoryLevels url = {}, locationId = {}, paramMap = {}",
            URL_LOCATION_ID_LEVELS, locationId, JSONUtil.toJsonStr(paramMap));

        return getLocationInventoryLevelPage(locationId, paramMap);
    }

    /**
     * 处理翻页查询
     *
     * @param locationId
     * @param paramMap
     * @return
     */
    private ShopifyPage<InventoryLevelList> getLocationInventoryLevelPage(Long locationId, Map<String, Object> paramMap) {
        String url = this.replaceUrlValue(getRequestUrl(URL_LOCATION_ID_LEVELS), "location_id", locationId);
        ShopifyPage<InventoryLevelList> productListPage =
            this.httpPagedResponseWithAccessToken(url, Method.GET, paramMap, InventoryLevelList.class);
        if (productListPage == null) {
            throw new ShopifyClientException(ShopifyErrorEnums.RESPONSE_CONTENT_EMPTY);
        }
        return productListPage;
    }

    /**
     * 非第一次查询，需根据page_info，进行翻页查询
     * 检索位置的库存水平列表。注意：从2019-10版本开始，此端点通过使用响应标头中提供的链接来实现分页。发送page参数将返回错误。要了解更多信息，请参阅向分页的REST Admin API端点发出请求。
     *
     * @param locationId
     * @param pageInfo
     * @param limit
     * @return
     */
    public ShopifyPage<InventoryLevelList> getLocationInventoryLevels(Long locationId, String pageInfo, int limit) {
        Map<String, Object> paramMap =
            MapUtil.builder(new HashMap<String, Object>()).put(getRequestUrl(PAGE_INFO_QUERY_PARAMETER), pageInfo).put("limit", limit)
                .build();
        log.info("LocationApi - getLocationInventoryLevels url = {}, locationId = {}, paramMap = {}",
            URL_LOCATION_ID_LEVELS, locationId, JSONUtil.toJsonStr(paramMap));

        return getLocationInventoryLevelPage(locationId, paramMap);
    }


}
