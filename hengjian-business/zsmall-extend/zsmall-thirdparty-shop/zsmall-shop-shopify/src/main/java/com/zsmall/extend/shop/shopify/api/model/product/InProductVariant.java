package com.zsmall.extend.shop.shopify.api.model.product;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-24 17:12
 */
@Data
@Accessors(chain = true)
public class InProductVariant {

    private Long id;

    private String barcode;

    @Alias(JsonConstants.OPTION_1)
    private String option1;

    @<PERSON>as(JsonConstants.OPTION_2)
    private String option2;

    @Alias(JsonConstants.OPTION_3)
    private String option3;

    private String price;

    private String sku;

    @<PERSON><PERSON>(JsonConstants.COMPARE_AT_PRICE)
    private String compareAtPrice;

    @<PERSON><PERSON>(JsonConstants.FULFILLMENT_SERVICE)
    private String fulfillmentService;

    @<PERSON><PERSON>(JsonConstants.INVENTORY_MANAGEMENT)
    private String inventoryManagement;

    private String weight;

    /**
     * The unit of measurement that applies to the product variant's weight.
     * If you don't specify a value for weight_unit, then the shop's default unit of measurement is applied.
     * Valid values: g, kg, oz, and lb.
     */
    @Alias(JsonConstants.WEIGHT_UNIT)
    private String weightUnit;

}
