package com.zsmall.extend.shop.shopify.api.model.inventory;

import cn.hutool.core.annotation.Alias;
import com.zsmall.extend.shop.shopify.constant.JsonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @date 2022-12-25 14:56
 */
@Data
@Accessors(chain = true)
public class InInventoryLevelAdjust {

    /**
     * The ID of the inventory item.
     * 库存物料的ID。
     */
    @Alias(JsonConstants.INVENTORY_ITEM_ID)
    private Long inventoryItemId;

    /**
     * The ID of the location that the inventory level belongs to. To find the ID of the location, use the Location
     * resource.
     * 库存级别所属位置的ID。要查找位置的ID，请使用位置资源。
     */
    @Alias(JsonConstants.LOCATION_ID)
    private Long locationId;

    /**
     * The amount to adjust the available inventory quantity. Send negative values to subtract from the current
     * available quantity. For example, "available_adjustment": 2 increases the current available quantity by 2, and
     * "available_adjustment": -3decreases the current available quantity by 3.
     * 调整可用库存数量的数量。发送负值以从当前可用数量中减去。例如，"available_adjustment": 2将当前可用数量增加2，
     * 并将"available_adjustment": -3当前可用数量减少3。
     */
    @Alias(JsonConstants.AVAILABLE_ADJUSTMENT)
    private Integer availableAdjustment;

}
