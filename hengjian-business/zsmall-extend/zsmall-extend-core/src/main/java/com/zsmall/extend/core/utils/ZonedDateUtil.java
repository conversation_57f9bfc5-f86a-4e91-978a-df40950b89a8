package com.zsmall.extend.core.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.zsmall.extend.core.enums.ZoneIdEnum;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 带时区时间
 */
public class ZonedDateUtil {

    /**
     * 获取当前系统当前时区时间
     *
     * @return
     */
    public static ZonedDateTime getZonedDateTimeNowOfDefault() {
        return ZonedDateTime.now(ZoneId.systemDefault());
    }

    /**
     * 获取当前上海时区时间（北京时间）
     *
     * @return
     */
    public static ZonedDateTime getZonedDateTimeNowOfShanghai() {
        return ZonedDateTime.now(ZoneId.of(ZoneIdEnum.CTT.getZoneIdName()));
    }

    /**
     * 获取当前巴黎时区时间
     *
     * @return
     */
    public static ZonedDateTime getZonedDateTimeNowOfParis() {
        return ZonedDateTime.now(ZoneId.of(ZoneIdEnum.ECT.getZoneIdName()));
    }

    /**
     * 获取当前美国东部标准时区（纽约、华盛顿）
     *
     * @return
     */
    public static ZonedDateTime getZonedDateTimeNowOfEST() {
        return ZonedDateTime.now(ZoneId.of(ZoneIdEnum.EST.getZoneIdName()));
    }

    /**
     * 获取当前东京时区时间
     *
     * @return
     */
    public static ZonedDateTime getZonedDateTimeNowOfTokyo() {
        return ZonedDateTime.now(ZoneId.of(ZoneIdEnum.JST.getZoneIdName()));
    }

    /**
     * 获取当前时间
     *
     * @param zoneId 可为空，默认时区00:00, 美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static ZonedDateTime getCurrentZoneDateTime(ZoneId zoneId) {
        if (zoneId == null) {
            return ZonedDateTime.now(ZoneId.of("+00:00"));
        }

        return ZonedDateTime.now(zoneId);
    }

    /**
     * 时间点转成带时区时间点
     *
     * @param localDateTime
     * @param zoneId        可为空，默认为00:00, 美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static ZonedDateTime local2ZoneDateTime(LocalDateTime localDateTime, ZoneId zoneId) {
        if (zoneId == null) {
            zoneId = ZoneId.of("+00:00");
        }
        System.out.println("local2ZoneDateTime LocalDateTime[not format] = " + localDateTime);
        return localDateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId);
    }

    /**
     * 时间点转成带时区时间点
     *
     * @param localDateTime
     * @param currentZoneId 可为空，默认为系统当前时区, 美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @param newZoneId     可为空，美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static ZonedDateTime local2ZoneDateTime(LocalDateTime localDateTime, ZoneId currentZoneId, ZoneId newZoneId) {
        if (currentZoneId == null) {
            currentZoneId = ZoneId.systemDefault();
        }
        if (newZoneId == null) {
            newZoneId = ZoneId.of("+00:00");
        }
        System.out.println("local2ZoneDateTime LocalDateTime[not format] = " + localDateTime);
        return localDateTime.atZone(currentZoneId).withZoneSameInstant(newZoneId);
    }

    /**
     * 时间点转成带时区时间点
     *
     * @param dateTimeString
     * @param zoneId         可为空，默认为00:00，美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static ZonedDateTime local2ZoneDateTime(String dateTimeString, ZoneId zoneId) {
        if (zoneId == null) {
            zoneId = ZoneId.of("+00:00");
        }
        LocalDateTime localDateTime = LocalDateTimeUtil.parse(dateTimeString, "yyyy-MM-dd HH:mm:ss");

        return local2ZoneDateTime(localDateTime, zoneId);
    }

    /**
     * 时间点转成带时区时间点
     *
     * @param dateTimeString
     * @param currentZoneId  可为空，默认为系统当前时区, 美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @param zoneId         可为空，默认为00:00，美国：ZoneId.of("-7：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static ZonedDateTime local2ZoneDateTime(String dateTimeString, ZoneId currentZoneId, ZoneId zoneId) {
        if (currentZoneId == null) {
            currentZoneId = ZoneId.systemDefault();
        }
        if (zoneId == null) {
            zoneId = ZoneId.of("+00:00");
        }
        LocalDateTime localDateTime = LocalDateTimeUtil.parse(dateTimeString, "yyyy-MM-dd HH:mm:ss");

        return local2ZoneDateTime(localDateTime, currentZoneId, zoneId);
    }

    /**
     * 时区时间转成ISODateTime 字符串
     *
     * @param localDateTime
     * @param zoneId        可为空，默认为00:00，美国：ZoneId.of("-07：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static String zoned2ISODateTime(LocalDateTime localDateTime, ZoneId zoneId) {
        if (zoneId == null) {
            zoneId = ZoneId.of("+00:00");
        }
        ZonedDateTime fromDate = ZonedDateUtil.local2ZoneDateTime(localDateTime, zoneId);
        System.out.println("zoned2ISODateTime LocalDateTime[not format] = " + fromDate);
        return fromDate.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(zoneId));
    }


    /**
     * 时区时间转成ISODateTime 字符串
     *
     * @param zonedDateTime
     * @param zoneId        可为空，默认为00:00，美国：ZoneId.of("-07：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static String zoned2ISODateTime(ZonedDateTime zonedDateTime, ZoneId zoneId) {
        ZonedDateTime fromDate = zonedDateTime;
        if (zoneId == null) {
            zoneId = ZoneId.of("+00:00");
        }
        fromDate = zonedDateTime.withZoneSameInstant(zoneId);
        System.out.println("zoned2ISODateTime ZonedDateTime[not format] = " + fromDate);
        return zonedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(zoneId));
    }

    /**
     * 时区时间转成DateTime对象
     *
     * @param zonedDateTime
     * @param zoneId        可为空，默认为00:00，美国：ZoneId.of("-07：00")， 中国：ZoneId.of("+08：00")
     * @return
     */
    public static DateTime zoned2DateTime(ZonedDateTime zonedDateTime, ZoneId zoneId) {
        ZonedDateTime fromDate = zonedDateTime;
        if (zoneId == null) {
            zoneId = ZoneId.of("+00:00");
        }
        fromDate = zonedDateTime.withZoneSameInstant(zoneId);
        System.out.println("zoned2DateTime ZonedDateTime[not format] = " + fromDate);
        return DateUtil.parse(zonedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(zoneId)));
    }

    public static void main(String[] args) {
        //系统默认时区
        System.out.println(ZoneId.systemDefault());
        //系统上海时区
        ZonedDateTime shanghaiZonedDateTime = ZonedDateTime.now();
        System.out.println(shanghaiZonedDateTime);
//    //系统巴黎时区
        ZonedDateTime parisZonedDateTime = ZonedDateTime.now(ZoneId.of("Europe/Paris"));
        ;
        System.out.println(parisZonedDateTime);
        //系统美国东部时区纽约时间 -05:00
        System.out.println(ZonedDateTime.now(ZoneId.of("-05:00")));
        //系统东京时区 Asia/Tokyo
        System.out.println(ZonedDateTime.now(ZoneId.of("Asia/Tokyo")));

//    //上海时区，转换为巴黎时区
        System.out.println("============transform 时区转换=============");
        String transformZonedDateTime = ZonedDateUtil.zoned2ISODateTime(shanghaiZonedDateTime, ZoneId.of("Europe/Paris"));
        System.out.println("transformZonedDateTime: " + transformZonedDateTime);
        transformZonedDateTime = ZonedDateUtil.zoned2ISODateTime(LocalDateTime.now(), ZoneId.of("Europe/Paris"));
        System.out.println("transformZonedDateTime: " + transformZonedDateTime);

        System.out.println("============transform 时区转换=============");
        LocalDateTime localDateTime = LocalDateTime.now();
        System.out.println("currentZonedDateTime: -----" + localDateTime.atZone(ZoneId.of("-05:00")));
        ZonedDateTime currentZonedDateTime = ZonedDateUtil.local2ZoneDateTime(localDateTime, ZoneId.of("-05:00"));
        System.out.println("currentZonedDateTime: " + currentZonedDateTime);

        LocalDateTime newLocalDateTime = localDateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of("-05"
                + ":00"))
            .toLocalDateTime();
        System.out.println("newLocalDateTime: " + newLocalDateTime);


    }
}
