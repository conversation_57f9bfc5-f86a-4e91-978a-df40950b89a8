<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.logistics</groupId>
        <artifactId>zsmall-logistics</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <artifactId>zsmall-logistics-tracking17</artifactId>
    <version>${zsmall.version}</version>
    <name>ZS-Mall 扩展模块 物流 17track</name>
    <description>
        zsmall-logistics-tracking17 扩展模块 物流 17track
    </description>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend</groupId>
            <artifactId>zsmall-extend-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
    </dependencies>


</project>
