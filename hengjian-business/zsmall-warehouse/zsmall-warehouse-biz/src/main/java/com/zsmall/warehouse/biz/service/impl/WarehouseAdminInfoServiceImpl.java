package com.zsmall.warehouse.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.excel.core.ExcelResult;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysUserImportVo;
import com.hengjian.system.listener.SysUserImportListener;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.warehouse.biz.listener.AdminWarehouseImportListener;
import com.zsmall.warehouse.biz.service.WarehouseAdminInfoService;
import com.zsmall.warehouse.biz.util.WarehouseCodeGenerator;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseAdminDeliveryCountry;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.domain.WarehouseDeliveryCountry;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseAdminInfoBo;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseBo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.AdminWarehouseExcelImportVo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAdminInfoVo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import com.zsmall.warehouse.entity.iservice.IWarehouseAdminDeliveryCountryService;
import com.zsmall.warehouse.entity.iservice.IWarehouseDeliveryCountryService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import com.zsmall.warehouse.entity.mapper.WarehouseAdminInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理端仓库信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WarehouseAdminInfoServiceImpl implements WarehouseAdminInfoService {

    private final WarehouseAdminInfoMapper baseMapper;
    private final WarehouseCodeGenerator warehouseCodeGenerator;
    private final IWarehouseService warehouseService;
    private final IWorldLocationService worldLocationService;
    private final IWarehouseAdminDeliveryCountryService warehouseAdminDeliveryCountryService;
    private final IWarehouseDeliveryCountryService warehouseDeliveryCountryService;
    private final WarehouseAdminInfoMapper warehouseAdminInfoMapper;
    private final IDownloadRecordService iDownloadRecordService;

    /**
     * 查询管理端仓库信息
     */
    @Override
    public WarehouseAdminInfoVo queryById(Long id){
        WarehouseAdminInfoVo w = baseMapper.selectVoById(id);
        //查询
        LambdaQueryWrapper<WarehouseAdminDeliveryCountry> q = new LambdaQueryWrapper<>();
        q.eq(WarehouseAdminDeliveryCountry::getWarehouseAdminInfoId,id);
        List<WarehouseAdminDeliveryCountry> wa = warehouseAdminDeliveryCountryService.getBaseMapper()
                                                                                        .selectList(q);
        w.setDistributionCountriesId(wa.stream()
                                       .map(WarehouseAdminDeliveryCountry::getWorldLocationId)
                                       .collect(Collectors.toList()));
        return w;
    }

    /**
     * 查询管理端仓库信息列表
     */
    @Override
    public TableDataInfo<WarehouseAdminInfoVo> queryPageList(WarehouseAdminInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WarehouseAdminInfo> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(WarehouseAdminInfo::getCreateTime );
        Page<WarehouseAdminInfoVo> result=new Page<>();
        if (bo.getTenantType().equals(TenantType.Manager.name())){
             result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        }else {
            result = TenantHelper.ignore(()->baseMapper.selectVoPage(pageQuery.build(), lqw));
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询管理端仓库信息列表
     */
    @Override
    public List<WarehouseAdminInfoVo> queryList(WarehouseAdminInfoBo bo) {
        LambdaQueryWrapper<WarehouseAdminInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WarehouseAdminInfo> buildQueryWrapper(WarehouseAdminInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WarehouseAdminInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getWarehouseType()!=null, WarehouseAdminInfo::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseTypeName()), WarehouseAdminInfo::getWarehouseType, bo.getWarehouseType());
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseName()), WarehouseAdminInfo::getWarehouseName, bo.getWarehouseName());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), WarehouseAdminInfo::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(bo.getWarehouseState() != null, WarehouseAdminInfo::getWarehouseState, bo.getWarehouseState());
        lqw.eq(bo.getSupportLogisticsAccount() != null, WarehouseAdminInfo::getSupportLogisticsAccount, bo.getSupportLogisticsAccount());
        lqw.eq(bo.getCountryId() != null, WarehouseAdminInfo::getCountryId, bo.getCountryId());
        lqw.eq(StringUtils.isNotBlank(bo.getCountry()), WarehouseAdminInfo::getCountry, bo.getCountry());
        lqw.eq(bo.getStateId() != null, WarehouseAdminInfo::getStateId, bo.getStateId());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), WarehouseAdminInfo::getState, bo.getState());
        lqw.eq(bo.getCityId() != null, WarehouseAdminInfo::getCityId, bo.getCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), WarehouseAdminInfo::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress1()), WarehouseAdminInfo::getAddress1, bo.getAddress1());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress2()), WarehouseAdminInfo::getAddress2, bo.getAddress2());
        lqw.eq(StringUtils.isNotBlank(bo.getZipCode()), WarehouseAdminInfo::getZipCode, bo.getZipCode());
        lqw.like(StringUtils.isNotBlank(bo.getManagerName()), WarehouseAdminInfo::getManagerName, bo.getManagerName());
        lqw.eq(StringUtils.isNotBlank(bo.getManagerPhone()), WarehouseAdminInfo::getManagerPhone, bo.getManagerPhone());
        lqw.eq(bo.getLongitude() != null, WarehouseAdminInfo::getLongitude, bo.getLongitude());
        lqw.eq(bo.getLatitude() != null, WarehouseAdminInfo::getLatitude, bo.getLatitude());
        return lqw;
    }

    /**
     * 新增管理端仓库信息
     */
    @Transactional
    @Override
    public Boolean insertByBo(WarehouseAdminInfoBo bo) {
        List<Long> countriesId = bo.getDistributionCountriesId();
        if (CollUtil.isEmpty(countriesId)){
            throw new RuntimeException("仓库 配送国家信息不能为空");
        }
        WarehouseAdminInfo add = MapstructUtils.convert(bo, WarehouseAdminInfo.class);
        //校验数据
        if (add != null) {
            validEntityBeforeSave(add);
        }
        //设置仓库信息
        String warehouseSystemCode = getWarehouseSystemCode(bo.getCountryId(), bo.getStateId(), bo.getState());
        add.setWarehouseSystemCode(warehouseSystemCode);
        add.setTenantId(LoginHelper.getTenantId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        //查找国家数据库信息
        List<WorldLocation> worldLocations = worldLocationService.getBaseMapper().selectBatchIds(countriesId);
        //处理超管发货仓库信息
        worldLocations.forEach(item->{
            WarehouseAdminDeliveryCountry w=new WarehouseAdminDeliveryCountry();
            w.setCountryCode(item.getLocationCode());
            w.setWarehouseAdminInfoId(add.getId());
            w.setWorldLocationId(item.getId());
            JSONObject js = item.getLocationOtherName();
            w.setCountryNameEn(js.getStr("en_US"));
            w.setCountryNameZh(js.getStr("zh_CN"));
            warehouseAdminDeliveryCountryService.save(w);
        });
        return flag;
    }

    /**
     * 获取系统仓库编码
     * @return
     */
    public String getWarehouseSystemCode(Long countryId, Long stateId, String boState ){
        WorldLocationVo country = worldLocationService.queryById(countryId);
        String locationCode = country.getLocationCode();
        if (stateId != null && StrUtil.isBlank(boState)) {
            WorldLocationVo state = worldLocationService.queryById(stateId);
            locationCode = state.getLocationCode();
        }
        return warehouseCodeGenerator.codeGenerate(BusinessCodeEnum.WarehouseSystemCode, locationCode);
    }

    /**
     * 修改管理端仓库信息
     */
    @Transactional
    @Override
    public Boolean updateByBo(WarehouseAdminInfoBo bo) {
        List<Long> distributionCountriesId = bo.getDistributionCountriesId();
        if (CollUtil.isEmpty(distributionCountriesId)){
            throw new RuntimeException("仓库 配送国家信息不能为空");
        }
        WarehouseAdminInfo update = MapstructUtils.convert(bo, WarehouseAdminInfo.class);
        WarehouseAdminInfo warehouseAdminInfo = validEntityBeforeUpdate(update);
        baseMapper.updateById(warehouseAdminInfo);
        //联动更新分销商仓库信息
        warehouseService.updateWarehouseInfoByAdmin(warehouseAdminInfo.getWarehouseSystemCode());
        //删除原来的发货仓库
        warehouseAdminDeliveryCountryService.getBaseMapper().removeByAdminId(bo.getId());
        //查询国家
        //查找国家数据库信息
        List<WorldLocation> worldLocations = worldLocationService.getBaseMapper().selectBatchIds(distributionCountriesId);
        worldLocations.forEach(item -> {
            WarehouseAdminDeliveryCountry w=new WarehouseAdminDeliveryCountry();
            w.setCountryCode(item.getLocationCode());
            w.setWarehouseAdminInfoId(bo.getId());
            w.setWorldLocationId(item.getId());
            JSONObject js = item.getLocationOtherName();
            w.setCountryNameEn(js.getStr("en_US"));
            w.setCountryNameZh(js.getStr("zh_CN"));
            warehouseAdminDeliveryCountryService.save(w);
        });
        //同步更新供应商仓库信息
        //查询当前超管仓库那个供应商在使用
        LambdaQueryWrapper<Warehouse> q = new LambdaQueryWrapper<>();
        q.eq(Warehouse::getWarehouseCode,bo.getWarehouseCode());
        List<Warehouse> warehouses =  TenantHelper.ignore(()->warehouseService.getBaseMapper().selectList(q));
        //更新供应商仓库信息
        warehouses.forEach(s->{
            //移除供应商原来的仓库
            warehouseDeliveryCountryService.getBaseMapper().removeByWarehouseId(s.getId());
            worldLocations.forEach(qaw->{
                WarehouseDeliveryCountry w = new WarehouseDeliveryCountry();
                w.setWarehouseId(s.getId());
                w.setCountryCode(qaw.getLocationCode());
                w.setWorldLocationId(qaw.getId());
                JSONObject js = qaw.getLocationOtherName();
                w.setCountryNameEn(js.getStr("en_US"));
                w.setCountryNameZh(js.getStr("zh_CN"));
                warehouseDeliveryCountryService.save(w);
            });
        });
        return  true;
    }

    /**
     * 更新前校验数据
     * @param update
     */
    private WarehouseAdminInfo validEntityBeforeUpdate(WarehouseAdminInfo update) {
        if (ObjectUtil.isEmpty(update.getId())){
            throw new RuntimeException(StrUtil.format("更新前校验数据失败，id不能为空"));
        }
        //查询仓库是否存在
        WarehouseAdminInfo warehouseAdminInfo = baseMapper.selectById(update.getId());
        if (warehouseAdminInfo == null){
            throw new RuntimeException(StrUtil.format("更新前校验数据失败，仓库id:{},不存在",update.getId()));
        }
        //仓库名称去重
        //先判断前端传的仓库名称是否等于数据库的
        if (!ObjectUtil.equals(update.getWarehouseName(),warehouseAdminInfo.getWarehouseName())){
            if (update.getWarehouseName().length()>20){
                throw new RuntimeException(StrUtil.format("仓库名称：{},最长20个字符",update.getWarehouseName()));
            }
            if (baseMapper.selectOne(Wrappers.<WarehouseAdminInfo>lambdaQuery().eq(WarehouseAdminInfo::getWarehouseName, update.getWarehouseName())) != null){
                throw new RuntimeException(StrUtil.format("更新前校验数据失败，仓库名称：{},已存在",update.getWarehouseName()));
            }
        }

        //仓库编码
        if (!ObjectUtil.equals(update.getWarehouseCode(),warehouseAdminInfo.getWarehouseCode())){
            if (update.getWarehouseCode().length()>20){
                throw new RuntimeException(StrUtil.format("更新前校验数据失败，仓库编码：{},最长20个字符",update.getWarehouseCode()));
            }
            //仓库编码去重
            if (baseMapper.selectOne(Wrappers.<WarehouseAdminInfo>lambdaQuery().eq(WarehouseAdminInfo::getWarehouseCode, update.getWarehouseCode())) != null){
                throw new RuntimeException(StrUtil.format("更新前校验数据失败，仓库编码: {},已存在",update.getWarehouseCode()));
            }
        }

        //判断仓管姓名
        if (ObjectUtil.isNotEmpty(update.getManagerName())){
            if (update.getManagerName().length()>50){
                throw new RuntimeException(StrUtil.format("仓管姓名：{},长度不能超过50",update.getManagerName()));
            }
            if (!ReUtil.isMatch("^[\\u4e00-\\u9fa5a-zA-Z\\s]*$",update.getManagerName())){
                throw new RuntimeException(StrUtil.format("仓管姓名：{},格式不正确",update.getManagerName()));
            }
        }
        //判断仓管电话
        if (ObjectUtil.isNotEmpty(update.getManagerPhone())){
            if (update.getManagerPhone().length()>12){
                throw new RuntimeException(StrUtil.format("仓管电话：{},长度不能超过12",update.getManagerPhone()));
            }
            if (!ReUtil.isMatch("^[0-9-]*$",update.getManagerPhone())){
                throw new RuntimeException(StrUtil.format("仓管电话：{},格式不正确",update.getManagerPhone()));
            }
        }
        warehouseAdminInfo.setWarehouseName(update.getWarehouseName());
        warehouseAdminInfo.setWarehouseCode(update.getWarehouseCode());
        warehouseAdminInfo.setZipCode(update.getZipCode());
        if (ObjectUtil.isNotEmpty(update.getManagerName())){
            warehouseAdminInfo.setManagerName(update.getManagerName());
        }
        if (ObjectUtil.isNotEmpty(update.getManagerPhone())){
            warehouseAdminInfo.setManagerPhone(update.getManagerPhone());
        }
        return warehouseAdminInfo;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WarehouseAdminInfo entity){
        //TODO 做一些数据校验,如唯一约束
        //校验仓库编码格式
        if (entity.getWarehouseName().length()>20){
            throw new RuntimeException(StrUtil.format("请求参数异常，仓库名称：{} 最长20个字符", entity.getWarehouseName()));
        }
        //仓库名称去重
        if (baseMapper.selectOne(Wrappers.<WarehouseAdminInfo>lambdaQuery().eq(WarehouseAdminInfo::getWarehouseName, entity.getWarehouseName())) != null){
            throw new RuntimeException(StrUtil.format("仓库名称：{} 已存在", entity.getWarehouseName()));
        }
        if (entity.getWarehouseCode().length()>20){
            throw new RuntimeException(StrUtil.format("仓库编码：{}最长20个字符",entity.getWarehouseCode()));
        }
        //仓库编码去重
        if (baseMapper.selectOne(Wrappers.<WarehouseAdminInfo>lambdaQuery().eq(WarehouseAdminInfo::getWarehouseCode, entity.getWarehouseCode())) != null){
            throw new RuntimeException(StrUtil.format("仓库编码: {}已存在",entity.getWarehouseCode()));
        }
        //判断邮编
        if (entity.getZipCode().length()>10){
            throw new RuntimeException(StrUtil.format("邮编: {}最长10个字符",entity.getZipCode()));
        }
        //判断邮编格式
        if (!ReUtil.isMatch("^[\\d-]{1,10}$", entity.getZipCode())){
            throw new RuntimeException(StrUtil.format("邮编：{}格式不正确",entity.getZipCode()));
        }
        if (entity.getCity().length()>20){
            throw  new RuntimeException(StrUtil.format("城市：{}最长20个字符",entity.getCity()));
        }
        //判断城市
        if (!ReUtil.isMatch("^[a-zA-Z\\s\\u4e00-\\u9fa5]+$",entity.getCity())){
            throw new RuntimeException(StrUtil.format("城市：{}格式不正确,仅支持英文/中文",entity.getCity()));
        }
        //街道
        if (entity.getAddress1().length()>50){
            throw new RuntimeException(StrUtil.format("街道：{},最长50个字符",entity.getAddress1()));
        }
        if (ObjectUtil.isNotEmpty(entity.getManagerName())){
            if (entity.getManagerName().length()>50){
                throw new RuntimeException(StrUtil.format("仓管姓名：{},长度不能超过50",entity.getManagerName()));
            }
            if (!ReUtil.isMatch("^[\\u4e00-\\u9fa5a-zA-Z\\s]*$",entity.getManagerName())){
                throw new RuntimeException(StrUtil.format("仓管姓名：{},格式不正确",entity.getManagerName()));
            }
        }
        if (ObjectUtil.isNotEmpty(entity.getManagerPhone())){
            if (entity.getManagerPhone().length()>12){
                throw new RuntimeException(StrUtil.format("仓管电话：{},长度不能超过12",entity.getManagerPhone()));
            }
            if (!ReUtil.isMatch("^[0-9-]*$",entity.getManagerPhone())){
                throw new RuntimeException(StrUtil.format("仓管电话：{} ,格式不正确",entity.getManagerPhone()));
            }
        }

    }

    /**
     * 批量删除管理端仓库信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public R editWarehouseAdminInfoState(Long id, Integer state) {
        WarehouseAdminInfo warehouseAdminInfo = baseMapper.selectById(id);
        if (warehouseAdminInfo == null){
            return R.fail(StrUtil.format("更新前校验数据失败，仓库主键ID：{}不存在", id));
        }
        //判断State是不是0或者1
        if (state!=0 && state!=1){
            return R.fail(StrUtil.format("更新前校验数据失败，错误的上下架状态：{}", state));
        }
         //如果是超管禁用仓库，则判断超管仓库编码是否被供应商使用
        if (state==0){
            WarehouseBo warehouseBo = new WarehouseBo();
            warehouseBo.setWarehouseCode(warehouseAdminInfo.getWarehouseCode());
            warehouseBo.setWarehouseState(1);
            List<WarehouseVo> warehouseVos =  TenantHelper.ignore(()->warehouseService.queryList(warehouseBo));
            if (!warehouseVos.isEmpty()){
                return R.fail(501, StrUtil.format("该仓库被供应商使用，无法禁用;请通知供应商将仓库停止使用;停止之后即可禁用"));
            }
        }
        TenantHelper.ignore(()->baseMapper.update(null, Wrappers.<WarehouseAdminInfo>lambdaUpdate().eq(WarehouseAdminInfo::getId, id).set(WarehouseAdminInfo::getWarehouseState, state)));
        return R.ok(state==0? "禁用成功":"启用成功");
    }

    @Override
    public void excelImport(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            String fileName = file.getOriginalFilename();
            // 获取文件的大小
            String fileSize = String.valueOf(file.getSize());
            ExcelUtil.importExcel(inputStream, AdminWarehouseExcelImportVo.class, new AdminWarehouseImportListener(warehouseAdminInfoMapper,
                warehouseCodeGenerator,
                worldLocationService,
                warehouseAdminDeliveryCountryService,
                iDownloadRecordService,
                fileName,
                fileSize));
        } catch (IOException e) {
            log.error("管理仓库excel导入出现异常，文件[{}]导入失败", file.getOriginalFilename(), e);
            throw new ServiceException("文件导入失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("未知错误", e);
            throw new ServiceException("文件导入异常：" + e.getClass());
        }
    }
}
