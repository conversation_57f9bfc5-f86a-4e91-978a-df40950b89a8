package com.zsmall.activity.entity.domain.vo.chinaSpotProduct;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应体-国内现货商品详情
 *
 * <AUTHOR>
 * @date 2023/1/12
 */
@Data
@NoArgsConstructor
public class ChinaSpotProductDetailVo {

    /**
     * 商品编号
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 分类名中文
     */
    private String categoryName_zh_CN;
    /**
     * 分类名英文
     */
    private String categoryName_en_US;
    /**
     * 最小购买数量
     */
    private Integer minimumQuantity;
    /**
     * 国内现货商品Sku列表
     */
    private List<RespChinaSpotProductSkuDetailBody> chinaSpotProductSkuList;

}
