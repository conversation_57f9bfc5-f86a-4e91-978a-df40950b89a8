package com.zsmall.product.entity.domain.bo.member;


import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.product.entity.domain.member.MemberDiscount;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

/**
 * 会员等级折扣业务对象 member_discount
 *
 * <AUTHOR> Li
 * @date 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberDiscount.class, reverseConvertGenerate = false)
public class MemberDiscountBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 字典表等级相关code
     */
    @NotNull(message = "字典表等级相关code不能为空", groups = { AddGroup.class })
    private Long dictCode;

    /**
     * 会员等级名称
     */
    private String memberName;

    /**
     * 加点系数
     */
    @NotNull(message = "折扣系数不能为空", groups = { AddGroup.class, EditGroup.class })
    @Max(value = 30,message = "折扣系数最大30",groups = { AddGroup.class, EditGroup.class })
    @Min(value = 0,message = "折扣系数最小0",groups = { AddGroup.class, EditGroup.class })
    private Integer beforeMemberDiscount;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class })
    private Integer memberState;
}
