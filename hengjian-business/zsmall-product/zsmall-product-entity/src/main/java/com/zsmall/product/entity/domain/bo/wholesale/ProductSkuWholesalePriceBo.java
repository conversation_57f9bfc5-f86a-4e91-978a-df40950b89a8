package com.zsmall.product.entity.domain.bo.wholesale;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductSkuWholesalePrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 国外现货批发商品SKU价格业务对象 product_sku_wholesale_price
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuWholesalePrice.class, reverseConvertGenerate = false)
public class ProductSkuWholesalePriceBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品主键
     */
    @NotNull(message = "商品主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 商品SKU主键
     */
    @NotNull(message = "商品SKU主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuId;

    /**
     * 阶梯定价表主键
     */
    @NotNull(message = "阶梯定价表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tieredPriceId;

    /**
     * 原始单价（供货商）
     */
    @NotNull(message = "原始单价（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originUnitPrice;

    /**
     * 平台单价（员工、分销商）
     */
    @NotNull(message = "平台单价（员工、分销商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal platformUnitPrice;


}
