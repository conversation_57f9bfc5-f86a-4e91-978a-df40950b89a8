package com.zsmall.product.entity.domain.bo.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求体-商品附件
 *
 * <AUTHOR>
 * @date 2023/6/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductAttachmentBo {

    /**
     * 主键
     */
    private Long id;
    /**
     * 对象存储主键
     */
    private String ossId;
    /**
     * 附件原名
     */
    private String attachmentName;
    /**
     * 附件存储路径
     */
    private String attachmentSavePath;
    /**
     * 附件展示URL
     */
    private String attachmentShowUrl;
    /**
     * 附件排序
     */
    private String attachmentSort;
    /**
     * 附件类型
     */
    private String attachmentType;

}
