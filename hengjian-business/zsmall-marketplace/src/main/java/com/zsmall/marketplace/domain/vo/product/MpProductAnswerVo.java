package com.zsmall.marketplace.domain.vo.product;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应体-商品SKU问题答复
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Data
public class MpProductAnswerVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 回复人名称（原字段：replyerName）
     */
    private String answerer;

    /**
     * 回复时间（原字段：replyTime）
     */
    @Alias("createTime")
    private String answerTime;

    /**
     * 回复内容
     */
    private String answer;

    /**
     * 回复编码
     */
    private String answerCode;

    /** 回复类型：Question-追问，Answer-回复 */
    private String type;

    /** 追问状态：Reported-已举报 */
    private String questionStatus;

}
