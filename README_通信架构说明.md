# 4G CAT1远传水表通信架构说明

## 系统架构概述

```
┌─────────────────┐    4G网络     ┌─────────────────┐
│   水表设备      │ ──────────→   │  您的Java服务器  │
│ (内置4G模块)    │               │  (TCP服务器)    │
│                 │ ←──────────   │                 │
└─────────────────┘               └─────────────────┘
        ↑                                   ↓
   主动连接服务器                      监听端口等待连接
   定期上报数据                        处理设备请求
   响应服务器指令                      发送控制指令
```

## 通信方式详解

### 1. **连接建立过程**

1. **水表端**：
   - 水表设备开机后，通过内置的4G CAT1模块连接到移动网络
   - 使用配置的服务器IP和端口主动发起TCP连接
   - 建立长连接，保持与服务器的通信

2. **服务器端**：
   - Java程序作为TCP服务器运行，监听指定端口（默认10086）
   - 接收水表设备的连接请求
   - 为每个连接的设备维护会话状态

### 2. **数据交互流程**

#### **水表主动上报**（最常见）
```
水表设备 ──[上报数据包]──→ Java服务器
水表设备 ←──[应答确认]──── Java服务器
```

#### **服务器主动查询**
```
Java服务器 ──[读取指令]──→ 水表设备
Java服务器 ←──[数据响应]── 水表设备
```

#### **服务器控制指令**
```
Java服务器 ──[控制指令]──→ 水表设备
Java服务器 ←──[执行结果]── 水表设备
```

## 网络配置要求

### **服务器端配置**

1. **公网IP要求**：
   - 您的Java服务器需要有公网IP地址
   - 或者通过路由器端口映射将内网服务器暴露到公网

2. **端口配置**：
   - 默认监听端口：10086
   - 确保防火墙开放此端口
   - 如果使用云服务器，需要在安全组中开放端口

3. **网络带宽**：
   - 每个水表设备的数据量很小（通常几百字节）
   - 支持大量设备并发连接

### **水表端配置**

1. **4G网络**：
   - 水表内置4G CAT1模块
   - 需要插入有效的4G SIM卡
   - 确保SIM卡有足够的流量

2. **服务器地址配置**：
   - 需要在水表中配置您的服务器IP和端口
   - 可以通过协议指令远程修改服务器地址

## 部署步骤

### **第一步：准备服务器环境**

```bash
# 1. 确保Java环境
java -version

# 2. 检查端口是否可用
netstat -an | grep 10086

# 3. 配置防火墙（CentOS/RHEL）
firewall-cmd --permanent --add-port=10086/tcp
firewall-cmd --reload

# 4. 配置防火墙（Ubuntu）
ufw allow 10086/tcp
```

### **第二步：启动Java服务器**

```bash
# 编译并运行项目
mvn clean package
java -jar target/watermeter-service.jar

# 或者在IDE中直接运行
```

### **第三步：配置水表设备**

1. **设置服务器地址**：
   - 服务器IP：您的公网IP
   - 服务器端口：10086
   - 可以通过协议指令远程配置

2. **设置上报模式**：
   - 定时上报：每24小时上报一次
   - 触发上报：阀门动作、按键触摸等
   - 应急上报：欠费关阀时的应急通信

### **第四步：验证连接**

```bash
# 查看服务器日志
tail -f logs/watermeter.log

# 检查连接状态
curl http://localhost:8080/watermeter/api/watermeter/status/设备地址
```

## 常见问题解决

### **1. 设备无法连接**

**可能原因**：
- 服务器IP配置错误
- 端口被防火墙阻挡
- 4G网络信号不好
- SIM卡欠费或无流量

**解决方法**：
```bash
# 检查端口监听状态
netstat -tlnp | grep 10086

# 检查防火墙状态
iptables -L -n | grep 10086

# 测试端口连通性
telnet 您的服务器IP 10086
```

### **2. 连接频繁断开**

**可能原因**：
- 网络不稳定
- 心跳超时设置过短
- 服务器资源不足

**解决方法**：
- 调整心跳超时时间
- 增加重连机制
- 优化服务器性能

### **3. 数据解析错误**

**可能原因**：
- 协议版本不匹配
- 数据包损坏
- 编码格式错误

**解决方法**：
- 检查协议版本
- 增加数据校验
- 记录原始数据包用于调试

## 监控和维护

### **系统监控**

1. **连接监控**：
   ```java
   // 获取在线设备数量
   int onlineCount = messageHandler.getOnlineDeviceCount();
   
   // 获取在线设备列表
   String[] devices = messageHandler.getOnlineDevices();
   ```

2. **性能监控**：
   - 监控内存使用情况
   - 监控网络连接数
   - 监控数据处理延迟

### **日志管理**

```yaml
# application.yml 日志配置
logging:
  level:
    com.watermeter: DEBUG
  file:
    name: logs/watermeter.log
    max-size: 10MB
    max-history: 30
```

### **数据备份**

- 定期备份设备数据
- 备份系统配置
- 建立数据恢复机制

## API使用示例

### **检查设备连接状态**
```bash
curl http://localhost:8080/watermeter/api/watermeter/status/12345678901234
```

### **读取设备数据**
```bash
curl http://localhost:8080/watermeter/api/watermeter/data/12345678901234
```

### **批量读取设备数据**
```bash
curl -X POST http://localhost:8080/watermeter/api/watermeter/data/batch \
  -H "Content-Type: application/json" \
  -d '{"meterAddresses": ["12345678901234", "12345678901235"]}'
```

## 扩展功能

### **负载均衡**
- 使用Nginx进行TCP负载均衡
- 支持多台服务器集群部署

### **数据存储**
- 集成MySQL存储设备数据
- 使用Redis缓存热点数据

### **消息队列**
- 使用RabbitMQ处理大量设备消息
- 实现异步数据处理

### **Web管理界面**
- 开发设备管理界面
- 实时监控设备状态
- 历史数据查询和分析

---

**重要提醒**：
1. 确保服务器有公网IP或正确配置端口映射
2. 水表设备需要配置正确的服务器地址
3. 保持4G网络连接稳定
4. 定期检查系统日志和设备状态
