package com.hengjian.extend.verifycode.constant;

public class VerifyCodeConstant {

    public static final String MID_REGISTER_KEY = Type.REGISTER + ":";
    public static final String MID_PAYMENT_KEY = Type.PAYMENT_PASSWORD + ":";
    public static final String MID_FORGET_PASSWORD_KEY = Type.FORGET_PASSWORD + ":";

    /**
     * 系统配置关键字
     */
    public static class Config {
        /**
         * 是否开启默认短信密码
         */
        public static final String KEY_VERIFYCODE_ENABLE = "sys.verifycode.default.enable";
        /**
         * 指定的默认短信密码
         */
        public static final String KEY_VERIFYCODE_DEFAULT_CODE = "sys.verifycode.default.code";
        /**
         * 短信验证码模板或模板内容
         */
        public static final String KEY_SMS_TEMPLATE_VERIFYCODE = "sys.sms.template.verifycode";
        /**
         * 邮箱验证码模板或模板内容
         */
        public static final String KEY_EMAIL_TEMPLATE_VERIFYCODE = "sys.email.template.verifycode";
        /**
         * 邮箱新没密码提醒模板或者模板内容
         */
        public static final String KEY_EMAIL_TEMPLATE_NEWPASSWORD = "sys.email.template.newpasswod";
        /**
         * 支付密码-短信验证码模板或模板内容
         */
        public static final String KEY_SMS_TEMPLATE_VERIFYCODE_PAYMENT = "sys.sms.template.verifycode.payment";
        /**
         * 支付密码-邮箱验证码模板或模板内容
         */
        public static final String KEY_EMAIL_TEMPLATE_VERIFYCODE_PAYMENT = "sys.email.template.verifycode.payment";
        /**
         * 商品活动到期提醒邮件模板
         */
        public static final String KEY_EMAIL_TEMPLATE_ACTIVITY_EXPIRATION = "sys.email.template.activityExpiration";
        /**
         * 商品活动到期提醒短信模板
         */
        public static final String KEY_SMS_TEMPLATE_ACTIVITY_EXPIRATION = "sys.sms.template.activityExpiration";
        /**
         * 用户反馈答复邮件模板
         */
        public static final String KEY_EMAIL_TEMPLATE_USER_FEEDBACK_REPLY = "sys.email.template.userFeedbackReply";
        /**
         * 忘记密码短信验证码模板
         */
        public static final String KEY_SMS_TEMPLATE_VERIFYCODE_FORGOTPASSWORD = "sys.sms.template.verifycode.forgotpassword";
        /**
         * 忘记密码邮箱验证码模板
         */
        public static final String KEY_EMAIL_TEMPLATE_VERIFYCODE_FORGOTPASSWORD = "sys.email.template.verifycode.forgotpassword";
        /**
         * 供应商入驻通知短信模板 - 拒绝
         */
        public static final String KEY_SMS_TEMPLATE_SETTLEIN_REJECTED = "sys.sms.settlein.review.notice.rejected";
        /**
         * 供应商入驻通知邮箱模板 - 拒绝
         */
        public static final String KEY_EMAIL_TEMPLATE_SETTLEIN_REJECTED = "sys.email.settlein.review.notice.rejected";
        /**
         * 供应商入驻通知短信模板 - 通过
         */
        public static final String KEY_SMS_TEMPLATE_SETTLEIN_ACCEPTED = "sys.sms.settlein.review.notice.accepted";
        /**
         * 供应商入驻通知邮箱模板 - 通过
         */
        public static final String KEY_EMAIL_TEMPLATE_SETTLEIN_ACCEPTED = "sys.email.settlein.review.notice.accepted";
    }


    /**
     * 验证码类型
     */
    public static class Type {
        /**
         * 注册
         */
        public static final String REGISTER = "REGISTER";
        /**
         * 登录
         */
        public static final String LOGIN = "LOGIN";
        /**
         * 支付密码
         */
        public static final String PAYMENT_PASSWORD = "PaymentPassword";
        /**
         * 忘记密码
         */
        public static final String FORGET_PASSWORD = "ForgetPassword";
    }


}
