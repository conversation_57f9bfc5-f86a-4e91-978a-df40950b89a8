package com.watermeter;

import com.watermeter.dto.WaterMeterData;
import com.watermeter.service.WaterMeterService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 水表服务测试类
 * 演示如何使用水表数据读取接口
 */
@SpringBootTest
public class WaterMeterServiceTest {
    
    @Autowired
    private WaterMeterService waterMeterService;
    
    /**
     * 测试读取单个水表数据
     */
    @Test
    public void testReadSingleMeterData() {
        try {
            // 水表地址（14位BCD码）
            String meterAddress = "12345678901234";
            
            // 读取水表数据
            WaterMeterData data = waterMeterService.readMeterData(meterAddress);
            
            // 输出结果
            System.out.println("=== 水表数据读取成功 ===");
            System.out.println("表地址: " + data.getMeterAddress());
            System.out.println("IMEI: " + data.getImei());
            System.out.println("IMSI: " + data.getImsi());
            System.out.println("ICCID: " + data.getIccid());
            System.out.println("硬件版本: " + data.getHardwareVersion());
            System.out.println("软件版本: " + data.getSoftwareVersion());
            System.out.println("计量模式: " + (data.getMeteringMode() != null ? data.getMeteringMode().getDescription() : "未知"));
            System.out.println("付费模式: " + (data.getPaymentMode() != null ? data.getPaymentMode().getDescription() : "未知"));
            System.out.println("当前阶梯: " + data.getCurrentTier());
            System.out.println("读取时间: " + data.getReadTime());
            
            // 输出服务器地址信息
            if (data.getServerAddress1() != null) {
                System.out.println("服务器地址1: " + data.getServerAddress1().getIp() + ":" + data.getServerAddress1().getPort());
            }
            
            // 输出阶梯参数
            if (data.getTierParameters() != null) {
                WaterMeterData.TierParameters tier = data.getTierParameters();
                System.out.println("阶梯量1: " + tier.getTier1Volume() + " m³");
                System.out.println("阶梯价格1: " + tier.getTier1Price() + " 分/m³");
            }
            
        } catch (Exception e) {
            System.err.println("读取水表数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试异步读取水表数据
     */
    @Test
    public void testReadMeterDataAsync() {
        String meterAddress = "12345678901234";
        
        waterMeterService.readMeterDataAsync(meterAddress)
                .thenAccept(data -> {
                    System.out.println("=== 异步读取成功 ===");
                    System.out.println("表地址: " + data.getMeterAddress());
                    System.out.println("IMEI: " + data.getImei());
                })
                .exceptionally(ex -> {
                    System.err.println("异步读取失败: " + ex.getMessage());
                    return null;
                });
    }
    
    /**
     * 测试批量读取水表数据
     */
    @Test
    public void testReadMultipleMeterData() {
        String[] meterAddresses = {
            "12345678901234",
            "12345678901235", 
            "12345678901236"
        };
        
        waterMeterService.readMultipleMeterData(meterAddresses)
                .thenAccept(dataArray -> {
                    System.out.println("=== 批量读取完成 ===");
                    for (int i = 0; i < dataArray.length; i++) {
                        WaterMeterData data = dataArray[i];
                        if (data != null) {
                            System.out.println("设备" + (i+1) + " - 地址: " + data.getMeterAddress() + ", IMEI: " + data.getImei());
                        } else {
                            System.out.println("设备" + (i+1) + " - 读取失败");
                        }
                    }
                })
                .exceptionally(ex -> {
                    System.err.println("批量读取失败: " + ex.getMessage());
                    return null;
                });
    }
    
    /**
     * 测试检查连接状态
     */
    @Test
    public void testCheckConnection() {
        String meterAddress = "12345678901234";
        
        boolean isConnected = waterMeterService.checkMeterConnection(meterAddress);
        System.out.println("设备连接状态: " + (isConnected ? "在线" : "离线"));
    }
    
    /**
     * 测试读取基本信息
     */
    @Test
    public void testReadBasicInfo() {
        try {
            String meterAddress = "12345678901234";
            
            WaterMeterData basicInfo = waterMeterService.getMeterBasicInfo(meterAddress);
            
            System.out.println("=== 基本信息 ===");
            System.out.println("表地址: " + basicInfo.getMeterAddress());
            System.out.println("IMEI: " + basicInfo.getImei());
            System.out.println("硬件版本: " + basicInfo.getHardwareVersion());
            System.out.println("软件版本: " + basicInfo.getSoftwareVersion());
            System.out.println("厂商代码: " + String.format("0x%04X", basicInfo.getManufacturerCode()));
            
        } catch (Exception e) {
            System.err.println("读取基本信息失败: " + e.getMessage());
        }
    }
}
