package com.watermeter.service;

import com.watermeter.dto.WaterMeterData;
import com.watermeter.protocol.WaterMeterProtocolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 水表服务类
 * 提供水表数据读取的业务接口
 */
@Slf4j
@Service
public class WaterMeterService {

    @Autowired
    private WaterMeterMessageHandler messageHandler;

    /**
     * 读取水表参数数据
     * @param meterAddress 水表地址
     * @return 水表数据对象
     * @throws Exception 读取失败时抛出异常
     */
    public WaterMeterData readMeterData(String meterAddress) throws Exception {
        return readMeterData(meterAddress, 30); // 默认30秒超时
    }

    /**
     * 读取水表参数数据（带超时控制）
     * @param meterAddress 水表地址
     * @param timeoutSeconds 超时时间（秒）
     * @return 水表数据对象
     * @throws Exception 读取失败时抛出异常
     */
    public WaterMeterData readMeterData(String meterAddress, int timeoutSeconds) throws Exception {
        log.info("开始读取水表数据，表地址: {}", meterAddress);

        try {
            // 验证表地址格式
            validateMeterAddress(meterAddress);

            // 通过消息处理器读取数据
            CompletableFuture<WaterMeterData> future = messageHandler.readMeterParameters(meterAddress);

            // 等待响应（带超时控制）
            WaterMeterData meterData = future.get(timeoutSeconds, TimeUnit.SECONDS);

            log.info("成功读取水表数据，表地址: {}, IMEI: {}", meterAddress, meterData.getImei());
            return meterData;

        } catch (Exception e) {
            log.error("读取水表数据失败，表地址: {}, 错误: {}", meterAddress, e.getMessage(), e);
            throw new Exception("读取水表数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步读取水表参数数据
     * @param meterAddress 水表地址
     * @return CompletableFuture<WaterMeterData>
     */
    public CompletableFuture<WaterMeterData> readMeterDataAsync(String meterAddress) {
        try {
            validateMeterAddress(meterAddress);
            return messageHandler.readMeterParameters(meterAddress);
        } catch (Exception e) {
            CompletableFuture<WaterMeterData> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 批量读取多个水表的数据
     * @param meterAddresses 水表地址数组
     * @return CompletableFuture<WaterMeterData[]>
     */
    public CompletableFuture<WaterMeterData[]> readMultipleMeterData(String[] meterAddresses) {
        CompletableFuture<WaterMeterData>[] futures = new CompletableFuture[meterAddresses.length];

        for (int i = 0; i < meterAddresses.length; i++) {
            futures[i] = readMeterDataAsync(meterAddresses[i]);
        }

        return CompletableFuture.allOf(futures)
                .thenApply(v -> {
                    WaterMeterData[] results = new WaterMeterData[futures.length];
                    for (int i = 0; i < futures.length; i++) {
                        try {
                            results[i] = futures[i].get();
                        } catch (Exception e) {
                            log.error("获取水表数据失败，地址: {}", meterAddresses[i], e);
                            results[i] = null;
                        }
                    }
                    return results;
                });
    }

    /**
     * 检查水表连接状态
     * @param meterAddress 水表地址
     * @return true-连接正常，false-连接异常
     */
    public boolean checkMeterConnection(String meterAddress) {
        try {
            return messageHandler.isDeviceOnline(meterAddress);
        } catch (Exception e) {
            log.error("检查水表连接状态失败，地址: {}", meterAddress, e);
            return false;
        }
    }

    /**
     * 获取水表基本信息（仅包含设备标识信息）
     * @param meterAddress 水表地址
     * @return 包含基本信息的WaterMeterData对象
     */
    public WaterMeterData getMeterBasicInfo(String meterAddress) throws Exception {
        WaterMeterData fullData = readMeterData(meterAddress);

        // 创建只包含基本信息的对象
        WaterMeterData basicInfo = new WaterMeterData();
        basicInfo.setMeterAddress(fullData.getMeterAddress());
        basicInfo.setImei(fullData.getImei());
        basicInfo.setImsi(fullData.getImsi());
        basicInfo.setIccid(fullData.getIccid());
        basicInfo.setHardwareVersion(fullData.getHardwareVersion());
        basicInfo.setSoftwareVersion(fullData.getSoftwareVersion());
        basicInfo.setMeterTypeCode(fullData.getMeterTypeCode());
        basicInfo.setManufacturerCode(fullData.getManufacturerCode());
        basicInfo.setReadTime(fullData.getReadTime());

        return basicInfo;
    }

    /**
     * 验证表地址格式
     * @param meterAddress 表地址
     * @throws IllegalArgumentException 地址格式不正确时抛出
     */
    private void validateMeterAddress(String meterAddress) {
        if (meterAddress == null || meterAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("水表地址不能为空");
        }

        // 移除空格和特殊字符
        String cleanAddress = meterAddress.replaceAll("[^0-9A-Fa-f]", "");

        // 检查长度（14位BCD码对应14个十六进制字符）
        if (cleanAddress.length() != 14) {
            throw new IllegalArgumentException("水表地址长度必须为14位: " + meterAddress);
        }

        // 检查是否为有效的十六进制字符
        if (!cleanAddress.matches("[0-9A-Fa-f]+")) {
            throw new IllegalArgumentException("水表地址包含无效字符: " + meterAddress);
        }
    }

    /**
     * 生成指令编号
     * @return 指令编号
     */
    private short generateCommandNumber() {
        // 简单的递增编号，实际应用中可能需要更复杂的生成策略
        return (short) (System.currentTimeMillis() & 0xFFFF);
    }
}
