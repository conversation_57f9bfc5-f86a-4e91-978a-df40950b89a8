package com.watermeter.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 水表TCP服务器
 * 作为服务器端，接收水表设备的连接和数据
 * 注意：水表通过4G网络主动连接到此服务器
 */
@Slf4j
@Component
public class WaterMeterTcpServer {

    // 设备连接缓存 - 存储已连接的水表设备
    private final ConcurrentHashMap<String, DeviceConnection> deviceConnections = new ConcurrentHashMap<>();

    // 服务器配置
    private static final int SERVER_PORT = 10086;     // 服务器监听端口
    private static final int READ_TIMEOUT = 30000;    // 30秒读取超时

    /**
     * 发送请求到水表设备
     * @param meterAddress 水表地址
     * @param requestData 请求数据包
     * @return CompletableFuture<byte[]> 响应数据
     */
    public CompletableFuture<byte[]> sendRequest(String meterAddress, byte[] requestData) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DeviceConnection connection = getOrCreateConnection(meterAddress);
                return sendAndReceive(connection, requestData);
            } catch (Exception e) {
                log.error("发送请求失败，表地址: {}", meterAddress, e);
                throw new RuntimeException("发送请求失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 检查设备连接状态
     * @param meterAddress 水表地址
     * @return true-已连接，false-未连接
     */
    public boolean isConnected(String meterAddress) {
        DeviceConnection connection = deviceConnections.get(meterAddress);
        return connection != null && connection.isConnected();
    }

    /**
     * 关闭指定设备的连接
     * @param meterAddress 水表地址
     */
    public void closeConnection(String meterAddress) {
        DeviceConnection connection = deviceConnections.remove(meterAddress);
        if (connection != null) {
            connection.close();
            log.info("已关闭设备连接，表地址: {}", meterAddress);
        }
    }

    /**
     * 关闭所有设备连接
     */
    public void closeAllConnections() {
        deviceConnections.values().forEach(DeviceConnection::close);
        deviceConnections.clear();
        log.info("已关闭所有设备连接");
    }

    /**
     * 获取或创建设备连接
     */
    private DeviceConnection getOrCreateConnection(String meterAddress) throws IOException {
        DeviceConnection connection = deviceConnections.get(meterAddress);

        if (connection == null || !connection.isConnected()) {
            // 创建新连接
            connection = createConnection(meterAddress);
            deviceConnections.put(meterAddress, connection);
            log.info("创建新的设备连接，表地址: {}", meterAddress);
        }

        return connection;
    }

    /**
     * 创建设备连接
     */
    private DeviceConnection createConnection(String meterAddress) throws IOException {
        SocketChannel channel = SocketChannel.open();
        channel.configureBlocking(true);

        // 连接到服务器（实际应用中可能需要根据设备地址确定服务器地址）
        InetSocketAddress serverAddress = new InetSocketAddress(DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT);

        try {
            // 设置连接超时
            channel.socket().connect(serverAddress, CONNECT_TIMEOUT);
            channel.socket().setSoTimeout(READ_TIMEOUT);

            log.info("成功连接到服务器: {}:{}", DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT);
            return new DeviceConnection(meterAddress, channel);

        } catch (IOException e) {
            channel.close();
            throw new IOException("连接服务器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送数据并接收响应
     */
    private byte[] sendAndReceive(DeviceConnection connection, byte[] requestData) throws IOException {
        SocketChannel channel = connection.getChannel();

        // 发送请求数据
        ByteBuffer sendBuffer = ByteBuffer.wrap(requestData);
        while (sendBuffer.hasRemaining()) {
            channel.write(sendBuffer);
        }

        log.debug("已发送请求数据，长度: {} 字节", requestData.length);

        // 接收响应数据
        ByteBuffer receiveBuffer = ByteBuffer.allocate(1024);
        int bytesRead = channel.read(receiveBuffer);

        if (bytesRead <= 0) {
            throw new IOException("未接收到响应数据");
        }

        byte[] responseData = new byte[bytesRead];
        receiveBuffer.flip();
        receiveBuffer.get(responseData);

        log.debug("已接收响应数据，长度: {} 字节", responseData.length);
        return responseData;
    }

    /**
     * 设备连接内部类
     */
    private static class DeviceConnection {
        private final String meterAddress;
        private final SocketChannel channel;
        private final long createTime;

        public DeviceConnection(String meterAddress, SocketChannel channel) {
            this.meterAddress = meterAddress;
            this.channel = channel;
            this.createTime = System.currentTimeMillis();
        }

        public boolean isConnected() {
            return channel != null && channel.isConnected() && !channel.socket().isClosed();
        }

        public void close() {
            try {
                if (channel != null) {
                    channel.close();
                }
            } catch (IOException e) {
                log.warn("关闭连接时发生异常，表地址: {}", meterAddress, e);
            }
        }

        public SocketChannel getChannel() {
            return channel;
        }

        public String getMeterAddress() {
            return meterAddress;
        }

        public long getCreateTime() {
            return createTime;
        }
    }
}
