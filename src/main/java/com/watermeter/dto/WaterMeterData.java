package com.watermeter.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 水表数据传输对象
 * 对应协议文档中读取表参数的应答数据
 */
@Data
public class WaterMeterData {
    
    /**
     * 表地址 - 14位BCD码
     */
    private String meterAddress;
    
    /**
     * IMEI - 15位BCD码
     */
    private String imei;
    
    /**
     * IMSI - 15位BCD码  
     */
    private String imsi;
    
    /**
     * ICCID - 20位BCD码
     */
    private String iccid;
    
    /**
     * 硬件版本号
     */
    private String hardwareVersion;
    
    /**
     * 软件版本号
     */
    private String softwareVersion;
    
    /**
     * 表类型代码
     */
    private Integer meterTypeCode;
    
    /**
     * 厂商代码
     */
    private Integer manufacturerCode;
    
    /**
     * 每日限制次数
     */
    private Integer dailyLimitCount;
    
    /**
     * 服务器地址1
     */
    private ServerAddress serverAddress1;
    
    /**
     * 服务器地址2
     */
    private ServerAddress serverAddress2;
    
    /**
     * 累计上报次数
     */
    private Integer totalReportCount;
    
    /**
     * 上报模式
     */
    private ReportMode reportMode;
    
    /**
     * 阀控功能屏蔽
     */
    private Integer valveControlMask;
    
    /**
     * 计量模式
     */
    private MeteringMode meteringMode;
    
    /**
     * 付费模式
     */
    private PaymentMode paymentMode;
    
    /**
     * 到位模式
     */
    private PositionMode positionMode;
    
    /**
     * 当前阶梯
     */
    private Integer currentTier;
    
    /**
     * 结算月
     */
    private Integer settlementMonth;
    
    /**
     * 结算日
     */
    private Integer settlementDay;
    
    /**
     * 阶梯参数
     */
    private TierParameters tierParameters;
    
    /**
     * 流量门限参数
     */
    private FlowThresholdParameters flowThresholds;
    
    /**
     * 费用参数
     */
    private CostParameters costParameters;
    
    /**
     * 数据读取时间
     */
    private LocalDateTime readTime;
    
    // 内部类定义
    
    @Data
    public static class ServerAddress {
        private String ip;
        private Integer port;
    }
    
    @Data
    public static class ReportMode {
        private Integer mode; // 0xC0-间隔分钟, 0xC1-指定日期, 0xC2-指定小时, 0xC3-指定分钟
        private Integer[] parameters;
    }
    
    @Data
    public static class TierParameters {
        private Integer tier1Volume;  // 阶梯量1
        private Integer tier2Volume;  // 阶梯量2  
        private Integer tier3Volume;  // 阶梯量3
        private Integer tier1Price;   // 阶梯价格1
        private Integer tier2Price;   // 阶梯价格2
        private Integer tier3Price;   // 阶梯价格3
        private Integer tier4Price;   // 阶梯价格4
    }
    
    @Data
    public static class FlowThresholdParameters {
        private Integer highFlowThreshold;     // 大流量门限值
        private Integer highFlowTime;          // 大流量门限时间
        private Integer lowFlowThreshold;      // 小流量门限值
        private Integer lowFlowTime;           // 小流量门限时间
    }
    
    @Data
    public static class CostParameters {
        private Integer baseWaterAmount;       // 保底水量/金额
        private Integer extraWaterAmount;      // 额外水量/金额
        private Integer alarmAmount;           // 报警量/金额
        private Integer overdraftAmount;       // 透支量/金额
    }
    
    // 枚举定义
    
    public enum MeteringMode {
        MODE_1L(0x50, "1L"),
        MODE_10L(0x60, "10L"), 
        MODE_100L(0x70, "100L"),
        MODE_1000L(0x80, "1000L");
        
        private final int code;
        private final String description;
        
        MeteringMode(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public static MeteringMode fromCode(int code) {
            for (MeteringMode mode : values()) {
                if (mode.code == code) {
                    return mode;
                }
            }
            return null;
        }
        
        public int getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    public enum PaymentMode {
        POSTPAID(0x48, "后付费"),
        PREPAID(0x59, "预付费"),
        PREPAID_TIER(0x4A, "预阶梯"),
        HEATING_VALVE(0x4E, "暖通阀");
        
        private final int code;
        private final String description;
        
        PaymentMode(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public static PaymentMode fromCode(int code) {
            for (PaymentMode mode : values()) {
                if (mode.code == code) {
                    return mode;
                }
            }
            return null;
        }
        
        public int getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    public enum PositionMode {
        BLOCKING(0x44, "堵转"),
        SWITCH(0x4B, "开关");
        
        private final int code;
        private final String description;
        
        PositionMode(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public static PositionMode fromCode(int code) {
            for (PositionMode mode : values()) {
                if (mode.code == code) {
                    return mode;
                }
            }
            return null;
        }
        
        public int getCode() { return code; }
        public String getDescription() { return description; }
    }
}
