package com.watermeter.server;

import com.watermeter.protocol.WaterMeterProtocolUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 水表TCP服务器
 * 监听端口，接收水表设备的4G网络连接
 */
@Slf4j
@Component
public class WaterMeterTcpServer {
    
    @Value("${watermeter.server.port:10086}")
    private int serverPort;
    
    @Autowired
    private WaterMeterMessageHandler messageHandler;
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;
    
    // 存储已连接的水表设备
    private final ConcurrentHashMap<String, Channel> deviceChannels = new ConcurrentHashMap<>();
    
    /**
     * 启动TCP服务器
     */
    @PostConstruct
    public void startServer() {
        new Thread(this::doStartServer, "WaterMeter-Server").start();
    }
    
    private void doStartServer() {
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();
        
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // 空闲检测 - 60秒无数据则断开连接
                            pipeline.addLast(new IdleStateHandler(60, 0, 0, TimeUnit.SECONDS));
                            
                            // 协议解码器
                            pipeline.addLast(new WaterMeterProtocolDecoder());
                            
                            // 协议编码器
                            pipeline.addLast(new WaterMeterProtocolEncoder());
                            
                            // 业务处理器
                            pipeline.addLast(new WaterMeterChannelHandler(WaterMeterTcpServer.this));
                        }
                    });
            
            ChannelFuture future = bootstrap.bind(serverPort).sync();
            serverChannel = future.channel();
            
            log.info("水表TCP服务器启动成功，监听端口: {}", serverPort);
            log.info("等待水表设备连接...");
            
            // 等待服务器关闭
            future.channel().closeFuture().sync();
            
        } catch (Exception e) {
            log.error("启动TCP服务器失败", e);
        } finally {
            shutdown();
        }
    }
    
    /**
     * 关闭服务器
     */
    @PreDestroy
    public void shutdown() {
        log.info("正在关闭水表TCP服务器...");
        
        if (serverChannel != null) {
            serverChannel.close();
        }
        
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        
        deviceChannels.clear();
        log.info("水表TCP服务器已关闭");
    }
    
    /**
     * 注册设备连接
     */
    public void registerDevice(String meterAddress, Channel channel) {
        deviceChannels.put(meterAddress, channel);
        log.info("水表设备已连接: {} -> {}", meterAddress, channel.remoteAddress());
    }
    
    /**
     * 移除设备连接
     */
    public void removeDevice(String meterAddress) {
        Channel channel = deviceChannels.remove(meterAddress);
        if (channel != null) {
            log.info("水表设备已断开: {} -> {}", meterAddress, channel.remoteAddress());
        }
    }
    
    /**
     * 向指定设备发送数据
     */
    public boolean sendToDevice(String meterAddress, byte[] data) {
        Channel channel = deviceChannels.get(meterAddress);
        if (channel != null && channel.isActive()) {
            channel.writeAndFlush(data);
            log.debug("向设备发送数据: {}, 长度: {}", meterAddress, data.length);
            return true;
        } else {
            log.warn("设备未连接或连接已断开: {}", meterAddress);
            return false;
        }
    }
    
    /**
     * 获取在线设备数量
     */
    public int getOnlineDeviceCount() {
        return (int) deviceChannels.values().stream()
                .filter(Channel::isActive)
                .count();
    }
    
    /**
     * 检查设备是否在线
     */
    public boolean isDeviceOnline(String meterAddress) {
        Channel channel = deviceChannels.get(meterAddress);
        return channel != null && channel.isActive();
    }
    
    /**
     * 获取所有在线设备地址
     */
    public String[] getOnlineDevices() {
        return deviceChannels.entrySet().stream()
                .filter(entry -> entry.getValue().isActive())
                .map(entry -> entry.getKey())
                .toArray(String[]::new);
    }
    
    /**
     * 广播消息到所有在线设备
     */
    public void broadcastToAllDevices(byte[] data) {
        int sentCount = 0;
        for (String meterAddress : deviceChannels.keySet()) {
            if (sendToDevice(meterAddress, data)) {
                sentCount++;
            }
        }
        log.info("广播消息完成，发送到 {} 个设备", sentCount);
    }
}
