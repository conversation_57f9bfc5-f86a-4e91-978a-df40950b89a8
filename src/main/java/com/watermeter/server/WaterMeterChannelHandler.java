package com.watermeter.server;

import com.watermeter.dto.WaterMeterData;
import com.watermeter.protocol.WaterMeterProtocolUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * 水表通道处理器
 * 处理水表设备的连接、断开和数据接收
 */
@Slf4j
public class WaterMeterChannelHandler extends SimpleChannelInboundHandler<byte[]> {
    
    private final WaterMeterTcpServer server;
    private String meterAddress;
    
    public WaterMeterChannelHandler(WaterMeterTcpServer server) {
        this.server = server;
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.info("新的水表设备连接: {}", ctx.channel().remoteAddress());
        // 等待设备发送第一个数据包来识别设备地址
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        if (meterAddress != null) {
            server.removeDevice(meterAddress);
        }
        log.info("水表设备断开连接: {}", ctx.channel().remoteAddress());
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, byte[] data) {
        try {
            log.debug("接收到数据，长度: {} 字节", data.length);
            
            // 解析数据包获取设备地址
            String deviceAddress = extractMeterAddress(data);
            
            // 首次连接时注册设备
            if (meterAddress == null && deviceAddress != null) {
                meterAddress = deviceAddress;
                server.registerDevice(meterAddress, ctx.channel());
            }
            
            // 处理不同类型的数据包
            byte controlCode = data[10]; // 控制码位置
            
            switch (controlCode) {
                case WaterMeterProtocolUtil.CONTROL_READ_REQUEST:
                    // 处理读取请求（通常不会收到，因为我们是服务器）
                    handleReadRequest(ctx, data);
                    break;
                    
                case WaterMeterProtocolUtil.CONTROL_WRITE_REQUEST:
                    // 处理写入请求（通常不会收到，因为我们是服务器）
                    handleWriteRequest(ctx, data);
                    break;
                    
                case (byte) 0x97: // 设备主动上报
                    handleDeviceReport(ctx, data);
                    break;
                    
                case (byte) 0x81: // 读取响应
                    handleReadResponse(ctx, data);
                    break;
                    
                case (byte) 0x84: // 写入响应
                    handleWriteResponse(ctx, data);
                    break;
                    
                default:
                    log.warn("未知的控制码: 0x{}", String.format("%02X", controlCode));
                    break;
            }
            
        } catch (Exception e) {
            log.error("处理数据包时发生异常", e);
        }
    }
    
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.READER_IDLE) {
                log.warn("设备长时间无数据传输，关闭连接: {}", ctx.channel().remoteAddress());
                ctx.close();
            }
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("通道异常: {}", ctx.channel().remoteAddress(), cause);
        ctx.close();
    }
    
    /**
     * 从数据包中提取水表地址
     */
    private String extractMeterAddress(byte[] data) {
        if (data.length < 16) {
            return null;
        }
        
        try {
            // 表地址在第2-8字节位置（7字节）
            byte[] addressBytes = new byte[7];
            System.arraycopy(data, 2, addressBytes, 0, 7);
            
            // 转换为字符串格式
            StringBuilder sb = new StringBuilder();
            for (byte b : addressBytes) {
                sb.append(String.format("%02X", b));
            }
            return sb.toString();
            
        } catch (Exception e) {
            log.error("提取设备地址失败", e);
            return null;
        }
    }
    
    /**
     * 处理设备主动上报
     */
    private void handleDeviceReport(ChannelHandlerContext ctx, byte[] data) {
        log.info("收到设备主动上报，设备: {}", meterAddress);
        
        try {
            // 解析上报数据
            // 这里可以根据数据包类型进行不同的处理
            byte dataPacketType = data[16]; // 数据包类型位置
            
            switch (dataPacketType) {
                case 0x01: // 普通表上传数据包
                    handleNormalMeterReport(data);
                    break;
                case 0x02: // 预付费阶梯表上传数据包
                    handleTierMeterReport(data);
                    break;
                case 0x03: // 后付费表上传数据包
                    handlePostpaidMeterReport(data);
                    break;
                case 0x05: // 额外扣费上传数据包
                    handleExtraChargeReport(data);
                    break;
                case 0x06: // 历史数据上传数据包
                    handleHistoryDataReport(data);
                    break;
                default:
                    log.warn("未知的数据包类型: 0x{}", String.format("%02X", dataPacketType));
                    break;
            }
            
            // 发送应答
            sendReportResponse(ctx, data);
            
        } catch (Exception e) {
            log.error("处理设备上报失败", e);
        }
    }
    
    /**
     * 处理读取响应
     */
    private void handleReadResponse(ChannelHandlerContext ctx, byte[] data) {
        log.info("收到读取响应，设备: {}", meterAddress);
        
        try {
            // 解析读取响应数据
            WaterMeterData meterData = WaterMeterProtocolUtil.parseReadParametersResponse(data);
            
            // 这里可以将数据存储到数据库或缓存中
            // 也可以通知等待的请求线程
            log.info("解析水表数据成功: IMEI={}, 硬件版本={}", 
                    meterData.getImei(), meterData.getHardwareVersion());
            
        } catch (Exception e) {
            log.error("解析读取响应失败", e);
        }
    }
    
    /**
     * 处理写入响应
     */
    private void handleWriteResponse(ChannelHandlerContext ctx, byte[] data) {
        log.info("收到写入响应，设备: {}", meterAddress);
        
        // 检查写入是否成功
        if (data.length > 18) {
            byte successFlag = data[18]; // 成功标志位置
            if (successFlag == 0) {
                log.info("写入操作成功，设备: {}", meterAddress);
            } else {
                log.warn("写入操作失败，设备: {}, 错误代码: {}", meterAddress, successFlag);
            }
        }
    }
    
    /**
     * 处理读取请求（服务器收到的情况较少）
     */
    private void handleReadRequest(ChannelHandlerContext ctx, byte[] data) {
        log.debug("收到读取请求，设备: {}", meterAddress);
        // 通常服务器不会收到设备的读取请求
    }
    
    /**
     * 处理写入请求（服务器收到的情况较少）
     */
    private void handleWriteRequest(ChannelHandlerContext ctx, byte[] data) {
        log.debug("收到写入请求，设备: {}", meterAddress);
        // 通常服务器不会收到设备的写入请求
    }
    
    /**
     * 发送上报应答
     */
    private void sendReportResponse(ChannelHandlerContext ctx, byte[] originalData) {
        try {
            // 构造应答数据包
            byte[] response = buildReportResponse(originalData);
            ctx.writeAndFlush(response);
            log.debug("已发送上报应答，设备: {}", meterAddress);
            
        } catch (Exception e) {
            log.error("发送上报应答失败", e);
        }
    }
    
    /**
     * 构造上报应答数据包
     */
    private byte[] buildReportResponse(byte[] originalData) {
        // 根据协议构造应答包
        // 这里简化处理，实际应该根据协议详细构造
        byte[] response = new byte[19];
        
        // 复制基本信息
        System.arraycopy(originalData, 0, response, 0, 15);
        
        // 修改控制码为应答
        response[10] = 0x17; // 服务器对表端上报的响应
        
        // 设置数据长度
        response[15] = 0x02;
        
        // 设置数据包类型
        response[16] = originalData[16];
        
        // 设置关机标志（0xAF表示关机，0x00表示继续等待）
        response[17] = (byte) 0xAF; // 让设备关机
        
        // 计算校验码
        int checksum = 0;
        for (int i = 0; i < 18; i++) {
            checksum += response[i] & 0xFF;
        }
        response[18] = (byte) (checksum & 0xFF);
        
        return response;
    }
    
    // 处理不同类型的上报数据
    private void handleNormalMeterReport(byte[] data) {
        log.info("处理普通表上报数据，设备: {}", meterAddress);
        // 解析普通表数据
    }
    
    private void handleTierMeterReport(byte[] data) {
        log.info("处理阶梯表上报数据，设备: {}", meterAddress);
        // 解析阶梯表数据
    }
    
    private void handlePostpaidMeterReport(byte[] data) {
        log.info("处理后付费表上报数据，设备: {}", meterAddress);
        // 解析后付费表数据
    }
    
    private void handleExtraChargeReport(byte[] data) {
        log.info("处理额外扣费上报数据，设备: {}", meterAddress);
        // 解析额外扣费数据
    }
    
    private void handleHistoryDataReport(byte[] data) {
        log.info("处理历史数据上报，设备: {}", meterAddress);
        // 解析历史数据
    }
}
