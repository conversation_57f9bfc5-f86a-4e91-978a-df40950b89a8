package com.watermeter.server;

import com.watermeter.dto.WaterMeterData;
import com.watermeter.protocol.WaterMeterProtocolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 水表消息处理服务
 * 处理与水表设备的业务交互
 */
@Slf4j
@Service
public class WaterMeterMessageHandler {
    
    @Autowired
    private WaterMeterTcpServer tcpServer;
    
    // 存储等待响应的请求
    private final ConcurrentHashMap<String, CompletableFuture<byte[]>> pendingRequests = new ConcurrentHashMap<>();
    
    /**
     * 向指定水表发送读取参数请求
     * @param meterAddress 水表地址
     * @return CompletableFuture<WaterMeterData>
     */
    public CompletableFuture<WaterMeterData> readMeterParameters(String meterAddress) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 检查设备是否在线
                if (!tcpServer.isDeviceOnline(meterAddress)) {
                    throw new RuntimeException("设备不在线: " + meterAddress);
                }
                
                // 生成指令编号
                short commandNumber = generateCommandNumber();
                
                // 构造读取请求数据包
                byte[] requestPacket = WaterMeterProtocolUtil.buildReadParametersRequest(meterAddress, commandNumber);
                
                // 创建等待响应的Future
                String requestKey = meterAddress + "_" + commandNumber;
                CompletableFuture<byte[]> responseFuture = new CompletableFuture<>();
                pendingRequests.put(requestKey, responseFuture);
                
                // 发送请求
                boolean sent = tcpServer.sendToDevice(meterAddress, requestPacket);
                if (!sent) {
                    pendingRequests.remove(requestKey);
                    throw new RuntimeException("发送请求失败: " + meterAddress);
                }
                
                // 等待响应（30秒超时）
                byte[] responseData = responseFuture.get(30, TimeUnit.SECONDS);
                
                // 解析响应数据
                WaterMeterData meterData = WaterMeterProtocolUtil.parseReadParametersResponse(responseData);
                
                log.info("成功读取水表参数: {}", meterAddress);
                return meterData;
                
            } catch (Exception e) {
                log.error("读取水表参数失败: {}", meterAddress, e);
                throw new RuntimeException("读取水表参数失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 向指定水表发送阀门控制指令
     * @param meterAddress 水表地址
     * @param openValve true-开阀，false-关阀
     * @param forceOperation true-强制操作，false-非强制操作
     * @return CompletableFuture<Boolean>
     */
    public CompletableFuture<Boolean> controlValve(String meterAddress, boolean openValve, boolean forceOperation) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 检查设备是否在线
                if (!tcpServer.isDeviceOnline(meterAddress)) {
                    throw new RuntimeException("设备不在线: " + meterAddress);
                }
                
                // 生成指令编号
                short commandNumber = generateCommandNumber();
                
                // 构造阀门控制请求数据包
                byte[] requestPacket = buildValveControlRequest(meterAddress, commandNumber, openValve, forceOperation);
                
                // 创建等待响应的Future
                String requestKey = meterAddress + "_" + commandNumber;
                CompletableFuture<byte[]> responseFuture = new CompletableFuture<>();
                pendingRequests.put(requestKey, responseFuture);
                
                // 发送请求
                boolean sent = tcpServer.sendToDevice(meterAddress, requestPacket);
                if (!sent) {
                    pendingRequests.remove(requestKey);
                    throw new RuntimeException("发送阀门控制请求失败: " + meterAddress);
                }
                
                // 等待响应（30秒超时）
                byte[] responseData = responseFuture.get(30, TimeUnit.SECONDS);
                
                // 检查操作是否成功
                boolean success = checkOperationSuccess(responseData);
                
                log.info("阀门控制操作完成: {}, 操作: {}, 结果: {}", 
                        meterAddress, openValve ? "开阀" : "关阀", success ? "成功" : "失败");
                return success;
                
            } catch (Exception e) {
                log.error("阀门控制操作失败: {}", meterAddress, e);
                throw new RuntimeException("阀门控制操作失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 处理设备响应
     * @param meterAddress 设备地址
     * @param commandNumber 指令编号
     * @param responseData 响应数据
     */
    public void handleDeviceResponse(String meterAddress, short commandNumber, byte[] responseData) {
        String requestKey = meterAddress + "_" + commandNumber;
        CompletableFuture<byte[]> future = pendingRequests.remove(requestKey);
        
        if (future != null) {
            future.complete(responseData);
            log.debug("处理设备响应: {}, 指令编号: {}", meterAddress, commandNumber);
        } else {
            log.warn("未找到对应的等待请求: {}, 指令编号: {}", meterAddress, commandNumber);
        }
    }
    
    /**
     * 处理请求超时
     * @param meterAddress 设备地址
     * @param commandNumber 指令编号
     */
    public void handleRequestTimeout(String meterAddress, short commandNumber) {
        String requestKey = meterAddress + "_" + commandNumber;
        CompletableFuture<byte[]> future = pendingRequests.remove(requestKey);
        
        if (future != null) {
            future.completeExceptionally(new RuntimeException("请求超时"));
            log.warn("请求超时: {}, 指令编号: {}", meterAddress, commandNumber);
        }
    }
    
    /**
     * 获取在线设备列表
     */
    public String[] getOnlineDevices() {
        return tcpServer.getOnlineDevices();
    }
    
    /**
     * 获取在线设备数量
     */
    public int getOnlineDeviceCount() {
        return tcpServer.getOnlineDeviceCount();
    }
    
    /**
     * 检查设备是否在线
     */
    public boolean isDeviceOnline(String meterAddress) {
        return tcpServer.isDeviceOnline(meterAddress);
    }
    
    /**
     * 生成指令编号
     */
    private short generateCommandNumber() {
        return (short) (System.currentTimeMillis() & 0xFFFF);
    }
    
    /**
     * 构造阀门控制请求数据包
     */
    private byte[] buildValveControlRequest(String meterAddress, short commandNumber, boolean openValve, boolean forceOperation) {
        // 根据协议文档构造阀门控制数据包
        byte[] packet = new byte[29];
        int index = 0;
        
        // 起始帧
        packet[index++] = (byte) 0x68;
        
        // 表类型
        packet[index++] = (byte) 0x10;
        
        // 表地址 (7字节)
        byte[] addressBytes = convertMeterAddressToBytes(meterAddress);
        System.arraycopy(addressBytes, 0, packet, index, 7);
        index += 7;
        
        // 设备类型
        packet[index++] = (byte) 0x03;
        
        // 控制码
        packet[index++] = (byte) 0x04;
        
        // 指令编号
        packet[index++] = (byte) (commandNumber >> 8);
        packet[index++] = (byte) (commandNumber & 0xFF);
        
        // 备用
        packet[index++] = 0x00;
        packet[index++] = 0x00;
        
        // 数据长度
        packet[index++] = (byte) 0x0C;
        
        // 数据标识
        packet[index++] = (byte) 0xAA;
        packet[index++] = (byte) 0x05;
        
        // 阀门开/关
        packet[index++] = openValve ? (byte) 0x55 : (byte) 0x99;
        
        // 操作类型
        packet[index++] = forceOperation ? (byte) 0x5A : (byte) 0x00;
        
        // 备用 (8字节)
        for (int i = 0; i < 8; i++) {
            packet[index++] = 0x00;
        }
        
        // 计算校验码
        int checksum = 0;
        for (int i = 0; i < index; i++) {
            checksum += packet[i] & 0xFF;
        }
        packet[index++] = (byte) (checksum & 0xFF);
        
        // 结束帧
        packet[index] = (byte) 0x16;
        
        return packet;
    }
    
    /**
     * 将表地址字符串转换为字节数组
     */
    private byte[] convertMeterAddressToBytes(String address) {
        byte[] bytes = new byte[7];
        // 简化实现，实际应该根据BCD码格式转换
        for (int i = 0; i < 7 && i * 2 < address.length(); i++) {
            String hex = address.substring(i * 2, Math.min(i * 2 + 2, address.length()));
            bytes[i] = (byte) Integer.parseInt(hex, 16);
        }
        return bytes;
    }
    
    /**
     * 检查操作是否成功
     */
    private boolean checkOperationSuccess(byte[] responseData) {
        if (responseData.length > 18) {
            byte successFlag = responseData[18];
            return successFlag == 0;
        }
        return false;
    }
}
