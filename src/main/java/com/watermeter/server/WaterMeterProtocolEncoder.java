package com.watermeter.server;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

/**
 * 水表协议编码器
 * 将要发送的字节数组编码为ByteBuf
 */
@Slf4j
public class WaterMeterProtocolEncoder extends MessageToByteEncoder<byte[]> {
    
    @Override
    protected void encode(ChannelHandlerContext ctx, byte[] msg, ByteBuf out) {
        try {
            // 直接将字节数组写入ByteBuf
            out.writeBytes(msg);
            log.debug("编码数据包，长度: {} 字节", msg.length);
            
        } catch (Exception e) {
            log.error("编码数据包时发生异常", e);
        }
    }
}
