package com.watermeter.server;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 水表协议解码器
 * 将接收到的字节流解码为完整的协议数据包
 */
@Slf4j
public class WaterMeterProtocolDecoder extends ByteToMessageDecoder {
    
    private static final byte START_FRAME = (byte) 0x68;
    private static final byte END_FRAME = (byte) 0x16;
    private static final int MIN_PACKET_LENGTH = 17; // 最小数据包长度
    
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        // 检查是否有足够的数据
        if (in.readableBytes() < MIN_PACKET_LENGTH) {
            return;
        }
        
        // 标记读取位置
        in.markReaderIndex();
        
        try {
            // 查找起始帧
            int startIndex = findStartFrame(in);
            if (startIndex == -1) {
                // 没有找到起始帧，丢弃所有数据
                in.clear();
                return;
            }
            
            // 跳过无效数据到起始帧
            if (startIndex > 0) {
                in.skipBytes(startIndex);
                log.warn("跳过了 {} 字节的无效数据", startIndex);
            }
            
            // 检查是否有足够的数据读取数据长度字段
            if (in.readableBytes() < 16) {
                in.resetReaderIndex();
                return;
            }
            
            // 读取数据长度（第15字节，从0开始计数）
            int dataLength = in.getUnsignedByte(in.readerIndex() + 15);
            
            // 计算完整数据包长度
            int totalPacketLength = 17 + dataLength; // 固定头部17字节 + 数据域 + 校验码1字节 + 结束帧1字节
            
            // 检查是否接收到完整数据包
            if (in.readableBytes() < totalPacketLength) {
                in.resetReaderIndex();
                return;
            }
            
            // 验证结束帧
            byte endFrame = in.getByte(in.readerIndex() + totalPacketLength - 1);
            if (endFrame != END_FRAME) {
                log.warn("结束帧不正确: 0x{}, 期望: 0x{}", 
                        String.format("%02X", endFrame), 
                        String.format("%02X", END_FRAME));
                // 跳过起始帧，继续查找下一个数据包
                in.skipBytes(1);
                return;
            }
            
            // 读取完整数据包
            byte[] packet = new byte[totalPacketLength];
            in.readBytes(packet);
            
            // 验证校验码
            if (!validateChecksum(packet)) {
                log.warn("校验码验证失败，丢弃数据包");
                return;
            }
            
            log.debug("成功解码数据包，长度: {} 字节", packet.length);
            out.add(packet);
            
        } catch (Exception e) {
            log.error("解码数据包时发生异常", e);
            in.resetReaderIndex();
            in.skipBytes(1); // 跳过一个字节继续尝试
        }
    }
    
    /**
     * 查找起始帧位置
     */
    private int findStartFrame(ByteBuf buffer) {
        for (int i = buffer.readerIndex(); i < buffer.writerIndex(); i++) {
            if (buffer.getByte(i) == START_FRAME) {
                return i - buffer.readerIndex();
            }
        }
        return -1;
    }
    
    /**
     * 验证校验码
     */
    private boolean validateChecksum(byte[] packet) {
        if (packet.length < 3) {
            return false;
        }
        
        // 计算校验码（从起始帧到校验码前的所有字节）
        int calculatedChecksum = 0;
        for (int i = 0; i < packet.length - 2; i++) {
            calculatedChecksum += packet[i] & 0xFF;
        }
        calculatedChecksum &= 0xFF;
        
        // 获取数据包中的校验码
        int packetChecksum = packet[packet.length - 2] & 0xFF;
        
        boolean isValid = calculatedChecksum == packetChecksum;
        if (!isValid) {
            log.debug("校验码不匹配 - 计算值: 0x{}, 数据包值: 0x{}", 
                    String.format("%02X", calculatedChecksum),
                    String.format("%02X", packetChecksum));
        }
        
        return isValid;
    }
}
