package com.watermeter.protocol;

import com.watermeter.dto.WaterMeterData;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.time.LocalDateTime;

/**
 * 水表协议工具类
 * 处理协议数据包的编码和解码
 */
@Slf4j
public class WaterMeterProtocolUtil {
    
    // 协议常量
    public static final byte START_FRAME = (byte) 0x68;
    public static final byte END_FRAME = (byte) 0x16;
    public static final byte DEVICE_TYPE = (byte) 0x03;
    public static final byte WATER_METER_TYPE = (byte) 0x10;
    
    // 控制码
    public static final byte CONTROL_READ_REQUEST = (byte) 0x01;
    public static final byte CONTROL_READ_RESPONSE = (byte) 0x81;
    public static final byte CONTROL_WRITE_REQUEST = (byte) 0x04;
    public static final byte CONTROL_WRITE_RESPONSE = (byte) 0x84;
    
    // 数据标识
    public static final short DATA_ID_READ_PARAMS = (short) 0xA901;
    
    /**
     * 构造读取表参数的请求数据包
     * @param meterAddress 表地址(14位BCD码)
     * @param commandNumber 指令编号
     * @return 请求数据包字节数组
     */
    public static byte[] buildReadParametersRequest(String meterAddress, short commandNumber) {
        ByteBuffer buffer = ByteBuffer.allocate(28);
        
        // 1. 起始帧
        buffer.put(START_FRAME);
        
        // 2. 表类型
        buffer.put(WATER_METER_TYPE);
        
        // 3. 表地址 (7字节，14位BCD码，低字节在前)
        byte[] addressBytes = convertMeterAddressToBytes(meterAddress);
        buffer.put(addressBytes);
        
        // 4. 设备类型
        buffer.put(DEVICE_TYPE);
        
        // 5. 控制码
        buffer.put(CONTROL_READ_REQUEST);
        
        // 6. 指令编号 (2字节)
        buffer.putShort(commandNumber);
        
        // 7. 备用 (2字节)
        buffer.putShort((short) 0x0000);
        
        // 8. 数据长度
        buffer.put((byte) 0x0A);
        
        // 9. 数据标识
        buffer.putShort(DATA_ID_READ_PARAMS);
        
        // 10. 备用 (8字节)
        buffer.put(new byte[8]);
        
        // 11. 计算校验码
        byte[] data = buffer.array();
        byte checksum = calculateChecksum(data, 0, data.length - 2);
        buffer.put(data.length - 2, checksum);
        
        // 12. 结束帧
        buffer.put(END_FRAME);
        
        return buffer.array();
    }
    
    /**
     * 解析读取表参数的应答数据包
     * @param responseData 应答数据包字节数组
     * @return 解析后的水表数据对象
     */
    public static WaterMeterData parseReadParametersResponse(byte[] responseData) {
        if (responseData == null || responseData.length < 115) {
            throw new IllegalArgumentException("Invalid response data length");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(responseData);
        WaterMeterData data = new WaterMeterData();
        
        try {
            // 验证起始帧
            if (buffer.get() != START_FRAME) {
                throw new IllegalArgumentException("Invalid start frame");
            }
            
            // 跳过表类型
            buffer.get();
            
            // 解析表地址
            byte[] addressBytes = new byte[7];
            buffer.get(addressBytes);
            data.setMeterAddress(convertBytesToMeterAddress(addressBytes));
            
            // 跳过设备类型、控制码、指令编号、备用、数据长度、数据标识
            buffer.position(18);
            
            // 解析IMEI (8字节BCD码)
            byte[] imeiBytes = new byte[8];
            buffer.get(imeiBytes);
            data.setImei(convertBcdToString(imeiBytes, 15));
            
            // 解析IMSI (8字节BCD码)
            byte[] imsiBytes = new byte[8];
            buffer.get(imsiBytes);
            data.setImsi(convertBcdToString(imsiBytes, 15));
            
            // 解析ICCID (10字节BCD码)
            byte[] iccidBytes = new byte[10];
            buffer.get(iccidBytes);
            data.setIccid(convertBcdToString(iccidBytes, 20));
            
            // 解析硬件版本号
            short hardwareVersion = buffer.getShort();
            data.setHardwareVersion(String.format("%04X", hardwareVersion));
            
            // 解析软件版本号
            short softwareVersion = buffer.getShort();
            data.setSoftwareVersion(String.format("%04X", softwareVersion));
            
            // 解析表类型代码
            data.setMeterTypeCode((int) buffer.getShort());
            
            // 解析厂商代码
            data.setManufacturerCode((int) buffer.getShort());
            
            // 解析每日限制次数
            data.setDailyLimitCount((int) buffer.get() & 0xFF);
            
            // 解析服务器地址1
            data.setServerAddress1(parseServerAddress(buffer));
            
            // 解析服务器地址2
            data.setServerAddress2(parseServerAddress(buffer));
            
            // 解析累计上报次数
            data.setTotalReportCount((int) buffer.getShort() & 0xFFFF);
            
            // 解析上报模式
            data.setReportMode(parseReportMode(buffer));
            
            // 解析阀控功能屏蔽
            data.setValveControlMask((int) buffer.getShort() & 0xFFFF);
            
            // 解析计量模式
            int meteringModeCode = buffer.get() & 0xFF;
            data.setMeteringMode(WaterMeterData.MeteringMode.fromCode(meteringModeCode));
            
            // 解析付费模式
            int paymentModeCode = buffer.get() & 0xFF;
            data.setPaymentMode(WaterMeterData.PaymentMode.fromCode(paymentModeCode));
            
            // 解析到位模式
            int positionModeCode = buffer.get() & 0xFF;
            data.setPositionMode(WaterMeterData.PositionMode.fromCode(positionModeCode));
            
            // 解析当前阶梯
            data.setCurrentTier((int) buffer.get() & 0xFF);
            
            // 解析结算月
            data.setSettlementMonth((int) buffer.get() & 0xFF);
            
            // 解析结算日
            data.setSettlementDay((int) buffer.get() & 0xFF);
            
            // 解析阶梯参数
            data.setTierParameters(parseTierParameters(buffer));
            
            // 解析流量门限参数
            data.setFlowThresholds(parseFlowThresholds(buffer));
            
            // 解析费用参数
            data.setCostParameters(parseCostParameters(buffer));
            
            // 设置读取时间
            data.setReadTime(LocalDateTime.now());
            
            log.info("Successfully parsed water meter data for address: {}", data.getMeterAddress());
            return data;
            
        } catch (Exception e) {
            log.error("Error parsing water meter response data", e);
            throw new RuntimeException("Failed to parse water meter response", e);
        }
    }
    
    /**
     * 计算校验码
     */
    private static byte calculateChecksum(byte[] data, int start, int end) {
        int sum = 0;
        for (int i = start; i < end; i++) {
            sum += data[i] & 0xFF;
        }
        return (byte) (sum & 0xFF);
    }
    
    /**
     * 将表地址字符串转换为字节数组
     */
    private static byte[] convertMeterAddressToBytes(String address) {
        // 实现14位BCD码转换逻辑
        byte[] bytes = new byte[7];
        // 这里需要根据具体的地址格式实现转换逻辑
        return bytes;
    }
    
    /**
     * 将字节数组转换为表地址字符串
     */
    private static String convertBytesToMeterAddress(byte[] bytes) {
        // 实现BCD码到字符串的转换
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
    
    /**
     * BCD码转字符串
     */
    private static String convertBcdToString(byte[] bcdBytes, int length) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bcdBytes) {
            int high = (b >> 4) & 0x0F;
            int low = b & 0x0F;
            if (high <= 9) sb.append(high);
            if (low <= 9 && sb.length() < length) sb.append(low);
        }
        return sb.toString();
    }
    
    /**
     * 解析服务器地址
     */
    private static WaterMeterData.ServerAddress parseServerAddress(ByteBuffer buffer) {
        WaterMeterData.ServerAddress address = new WaterMeterData.ServerAddress();
        
        // IP地址 (4字节)
        byte[] ipBytes = new byte[4];
        buffer.get(ipBytes);
        address.setIp(String.format("%d.%d.%d.%d", 
            ipBytes[0] & 0xFF, ipBytes[1] & 0xFF, 
            ipBytes[2] & 0xFF, ipBytes[3] & 0xFF));
        
        // 端口号 (2字节，高字节在前)
        address.setPort((int) buffer.getShort() & 0xFFFF);
        
        return address;
    }
    
    /**
     * 解析上报模式
     */
    private static WaterMeterData.ReportMode parseReportMode(ByteBuffer buffer) {
        WaterMeterData.ReportMode reportMode = new WaterMeterData.ReportMode();
        
        byte[] modeBytes = new byte[6];
        buffer.get(modeBytes);
        
        reportMode.setMode((int) modeBytes[0] & 0xFF);
        
        Integer[] parameters = new Integer[5];
        for (int i = 1; i < 6; i++) {
            parameters[i-1] = (int) modeBytes[i] & 0xFF;
        }
        reportMode.setParameters(parameters);
        
        return reportMode;
    }
    
    /**
     * 解析阶梯参数
     */
    private static WaterMeterData.TierParameters parseTierParameters(ByteBuffer buffer) {
        WaterMeterData.TierParameters params = new WaterMeterData.TierParameters();
        
        params.setTier1Volume((int) buffer.getShort() & 0xFFFF);
        params.setTier2Volume((int) buffer.getShort() & 0xFFFF);
        params.setTier3Volume((int) buffer.getShort() & 0xFFFF);
        params.setTier1Price((int) buffer.getShort() & 0xFFFF);
        params.setTier2Price((int) buffer.getShort() & 0xFFFF);
        params.setTier3Price((int) buffer.getShort() & 0xFFFF);
        params.setTier4Price((int) buffer.getShort() & 0xFFFF);
        
        return params;
    }
    
    /**
     * 解析流量门限参数
     */
    private static WaterMeterData.FlowThresholdParameters parseFlowThresholds(ByteBuffer buffer) {
        WaterMeterData.FlowThresholdParameters params = new WaterMeterData.FlowThresholdParameters();
        
        params.setHighFlowThreshold((int) buffer.getShort() & 0xFFFF);
        params.setHighFlowTime((int) buffer.get() & 0xFF);
        params.setLowFlowThreshold((int) buffer.getShort() & 0xFFFF);
        params.setLowFlowTime((int) buffer.get() & 0xFF);
        
        return params;
    }
    
    /**
     * 解析费用参数
     */
    private static WaterMeterData.CostParameters parseCostParameters(ByteBuffer buffer) {
        WaterMeterData.CostParameters params = new WaterMeterData.CostParameters();
        
        params.setBaseWaterAmount((int) buffer.get() & 0xFF);
        params.setExtraWaterAmount((int) buffer.get() & 0xFF);
        params.setAlarmAmount((int) buffer.get() & 0xFF);
        params.setOverdraftAmount((int) buffer.get() & 0xFF);
        
        return params;
    }
}
