package com.watermeter.controller;

import com.watermeter.dto.WaterMeterData;
import com.watermeter.service.WaterMeterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 水表数据读取API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/watermeter")
public class WaterMeterController {
    
    @Autowired
    private WaterMeterService waterMeterService;
    
    /**
     * 读取单个水表的完整数据
     * @param meterAddress 水表地址
     * @return 水表数据
     */
    @GetMapping("/data/{meterAddress}")
    public ResponseEntity<Map<String, Object>> readMeterData(@PathVariable String meterAddress) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            WaterMeterData data = waterMeterService.readMeterData(meterAddress);
            
            response.put("success", true);
            response.put("message", "读取水表数据成功");
            response.put("data", data);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("读取水表数据失败", e);
            
            response.put("success", false);
            response.put("message", "读取水表数据失败: " + e.getMessage());
            response.put("data", null);
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 读取水表基本信息
     * @param meterAddress 水表地址
     * @return 水表基本信息
     */
    @GetMapping("/basic/{meterAddress}")
    public ResponseEntity<Map<String, Object>> readMeterBasicInfo(@PathVariable String meterAddress) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            WaterMeterData basicInfo = waterMeterService.getMeterBasicInfo(meterAddress);
            
            response.put("success", true);
            response.put("message", "读取水表基本信息成功");
            response.put("data", basicInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("读取水表基本信息失败", e);
            
            response.put("success", false);
            response.put("message", "读取水表基本信息失败: " + e.getMessage());
            response.put("data", null);
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 异步读取水表数据
     * @param meterAddress 水表地址
     * @return 异步任务结果
     */
    @GetMapping("/data/async/{meterAddress}")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> readMeterDataAsync(@PathVariable String meterAddress) {
        return waterMeterService.readMeterDataAsync(meterAddress)
                .thenApply(data -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "异步读取水表数据成功");
                    response.put("data", data);
                    return ResponseEntity.ok(response);
                })
                .exceptionally(ex -> {
                    log.error("异步读取水表数据失败", ex);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "异步读取水表数据失败: " + ex.getMessage());
                    response.put("data", null);
                    return ResponseEntity.badRequest().body(response);
                });
    }
    
    /**
     * 批量读取多个水表数据
     * @param request 包含水表地址数组的请求体
     * @return 批量读取结果
     */
    @PostMapping("/data/batch")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> readMultipleMeterData(@RequestBody BatchReadRequest request) {
        return waterMeterService.readMultipleMeterData(request.getMeterAddresses())
                .thenApply(dataArray -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "批量读取水表数据完成");
                    response.put("data", dataArray);
                    response.put("total", dataArray.length);
                    
                    // 统计成功和失败的数量
                    long successCount = java.util.Arrays.stream(dataArray).filter(data -> data != null).count();
                    response.put("successCount", successCount);
                    response.put("failureCount", dataArray.length - successCount);
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(ex -> {
                    log.error("批量读取水表数据失败", ex);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "批量读取水表数据失败: " + ex.getMessage());
                    response.put("data", null);
                    return ResponseEntity.badRequest().body(response);
                });
    }
    
    /**
     * 检查水表连接状态
     * @param meterAddress 水表地址
     * @return 连接状态
     */
    @GetMapping("/status/{meterAddress}")
    public ResponseEntity<Map<String, Object>> checkMeterConnection(@PathVariable String meterAddress) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isConnected = waterMeterService.checkMeterConnection(meterAddress);
            
            response.put("success", true);
            response.put("message", "检查连接状态成功");
            response.put("meterAddress", meterAddress);
            response.put("connected", isConnected);
            response.put("status", isConnected ? "在线" : "离线");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查水表连接状态失败", e);
            
            response.put("success", false);
            response.put("message", "检查连接状态失败: " + e.getMessage());
            response.put("connected", false);
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取水表设备信息摘要
     * @param meterAddress 水表地址
     * @return 设备信息摘要
     */
    @GetMapping("/summary/{meterAddress}")
    public ResponseEntity<Map<String, Object>> getMeterSummary(@PathVariable String meterAddress) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            WaterMeterData data = waterMeterService.readMeterData(meterAddress);
            
            // 构造摘要信息
            Map<String, Object> summary = new HashMap<>();
            summary.put("meterAddress", data.getMeterAddress());
            summary.put("imei", data.getImei());
            summary.put("hardwareVersion", data.getHardwareVersion());
            summary.put("softwareVersion", data.getSoftwareVersion());
            summary.put("meteringMode", data.getMeteringMode() != null ? data.getMeteringMode().getDescription() : "未知");
            summary.put("paymentMode", data.getPaymentMode() != null ? data.getPaymentMode().getDescription() : "未知");
            summary.put("positionMode", data.getPositionMode() != null ? data.getPositionMode().getDescription() : "未知");
            summary.put("currentTier", data.getCurrentTier());
            summary.put("readTime", data.getReadTime());
            
            response.put("success", true);
            response.put("message", "获取水表摘要信息成功");
            response.put("data", summary);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取水表摘要信息失败", e);
            
            response.put("success", false);
            response.put("message", "获取水表摘要信息失败: " + e.getMessage());
            response.put("data", null);
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 批量读取请求体
     */
    public static class BatchReadRequest {
        private String[] meterAddresses;
        
        public String[] getMeterAddresses() {
            return meterAddresses;
        }
        
        public void setMeterAddresses(String[] meterAddresses) {
            this.meterAddresses = meterAddresses;
        }
    }
}
