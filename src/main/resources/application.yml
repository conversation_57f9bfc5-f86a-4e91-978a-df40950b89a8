server:
  port: 8080
  servlet:
    context-path: /watermeter

spring:
  application:
    name: water-meter-service
  
  # 数据库配置（如果需要持久化）
  datasource:
    url: ***********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

# 水表通讯配置
watermeter:
  # 服务器配置
  server:
    host: *************
    port: 10086
    
  # 连接配置
  connection:
    timeout: 10000        # 连接超时时间（毫秒）
    read-timeout: 30000   # 读取超时时间（毫秒）
    max-connections: 100  # 最大连接数
    
  # 协议配置
  protocol:
    retry-count: 3        # 重试次数
    retry-interval: 1000  # 重试间隔（毫秒）
    
# 日志配置
logging:
  level:
    com.watermeter: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/watermeter.log
    max-size: 10MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
