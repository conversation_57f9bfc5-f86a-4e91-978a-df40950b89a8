package com.hengjian.openapi.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 应用-接口关联对象 sys_api_inf
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Data
@TableName("sys_api_inf")
public class SysApiInf  {


    private static final long serialVersionUID = 13107501501750L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * sys_api表主键ID
     */
    private Long sysApiId;

    /**
     * sys_inf表主键ID
     */
    private Long sysInfId;


}
