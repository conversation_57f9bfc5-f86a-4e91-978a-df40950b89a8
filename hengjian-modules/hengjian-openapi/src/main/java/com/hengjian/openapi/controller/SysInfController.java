package com.hengjian.openapi.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.openapi.domain.SysApi;
import com.hengjian.openapi.domain.SysApiInf;
import com.hengjian.openapi.domain.SysInf;
import com.hengjian.openapi.domain.bo.SysApiAddBo;
import com.hengjian.openapi.domain.bo.SysInfAddBo;
import com.hengjian.openapi.domain.bo.SysInfBo;
import com.hengjian.openapi.domain.vo.SysInfVo;
import com.hengjian.openapi.mapper.SysApiInfMapper;
import com.hengjian.openapi.mapper.SysApiMapper;
import com.hengjian.openapi.mapper.SysInfMapper;
import com.hengjian.openapi.service.ISysInfService;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应用接口
 *
 * <AUTHOR> Li
 * @date 2024-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/inf")
public class SysInfController extends BaseController {

    private final ISysInfService sysInfService;
    private final SysInfMapper sysInfMapper;
    private final SysApiMapper sysApiMapper;
    private final SysApiInfMapper sysApiInfMapper;


    /**
     * 查询应用接口列表
     */
    @SaCheckPermission("system:inf:list")
    @GetMapping("/list")
    public TableDataInfo<SysInfVo> list(SysInfBo bo, PageQuery pageQuery) {
        return sysInfService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应用接口列表
     */
    @SaCheckPermission("system:inf:export")
    @Log(title = "应用接口", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysInfBo bo, HttpServletResponse response) {
        List<SysInfVo> list = sysInfService.queryList(bo);
        ExcelUtil.exportExcel(list, "应用接口", SysInfVo.class, response,false);
    }

    /**
     * 获取应用接口详细信息
     *
     * @param apiId 主键
     */
    @SaCheckPermission("system:inf:query")
    @GetMapping("/{apiId}")
    public R<SysInfVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long apiId) {
        return R.ok(sysInfService.queryById(apiId));
    }

    /**
     * 新增应用接口
     */
    @SaCheckPermission("system:inf:add")
    @Log(title = "应用接口", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysInfBo bo) {
        return toAjax(sysInfService.insertByBo(bo));
    }

    /**
     * 修改应用接口
     */
    @SaCheckPermission("system:inf:edit")
    @Log(title = "应用接口", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysInfBo bo) {
        return toAjax(sysInfService.updateByBo(bo));
    }

    /**
     * 删除应用接口
     *
     * @param apiIds 主键串
     */

    /**
     * 删除应用接口
     * @param infIds 接口ID集合
     * @param apiId 应用ID
     * @return R
     */
    @SaCheckPermission("system:inf:remove")
    @Log(title = "应用接口", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infIds}/{apiId}")
    public R<Void> remove(@NotEmpty(message = "接口ID不能为空") @PathVariable Long[] infIds,
                          @NotNull(message = "应用ID不能为空") @PathVariable Long apiId) {
        return toAjax(sysInfService.deleteWithValidByIds(List.of(infIds), apiId,true));
    }

    /**
     * 创建接口并为应用分配
     * @param sysInfAddBo
     * @return
     */
    @GetMapping("/addSysInf")
    public R<SysInf> addSysInf(@RequestBody SysInfAddBo sysInfAddBo) {
        SysInf sysInf =new SysInf();
        sysInf.setInfId(IdUtil.getSnowflakeNextId());
        sysInf.setInfName(sysInfAddBo.getInfName());
        sysInf.setInfType(1);
        sysInf.setInfUrl(sysInfAddBo.getInfUrl());
        sysInf.setInfTestUrl(sysInfAddBo.getInfUrl());
        sysInf.setInfNote("恒健对外");
        sysInf.setRequestType(1);
        sysInf.setIsTest(2);
        sysInf.setCreateTime(new Date());
        sysInf.setUpdateTime(new Date());
        sysInfMapper.insert(sysInf);
        if (sysInfAddBo.getIsAllocation()){
            List<SysApi> sysApis = sysApiMapper.selectList();
            if (CollUtil.isNotEmpty(sysInfAddBo.getExcludeApiIds())){
                sysApis.removeIf(s->sysInfAddBo.getExcludeApiIds().contains(s.getApiId()));
            }
            sysApis.forEach(s->{
                SysApiInf sysApiInf=new SysApiInf();
                sysApiInf.setId(IdUtil.getSnowflake().nextId());
                sysApiInf.setSysApiId(s.getApiId());
                sysApiInf.setSysInfId(sysInf.getInfId());
                sysApiInfMapper.insert(sysApiInf);
            });
        }
        return R.ok(sysInf);
    }


    /**
     * 创建应用，并分配所有的接口
     * @param sysInfAddBo
     * @return
     */
    @GetMapping("/addSysApi")
    public R<SysApi> addSysApi(@RequestBody SysApiAddBo sysInfAddBo) {
        SysApi sysApi = BeanUtil.copyProperties(sysInfAddBo, SysApi.class);
        sysApi.setApiId(IdUtil.getSnowflakeNextId());
        //校验租户ID是否存在

        sysApiMapper.insert(sysApi);
        if (sysInfAddBo.getIsAllocation()){
            LambdaQueryWrapper<SysInf> q = new LambdaQueryWrapper<>();
            q.eq(SysInf::getDelFlag,0);
            q.eq(SysInf::getInfType,1);
            List<SysInf> sysInfs = sysInfMapper.selectList(q);
            if (CollUtil.isNotEmpty(sysInfAddBo.getExcludeInfIds())){
                sysInfs.removeIf(s->sysInfAddBo.getExcludeInfIds().contains(s.getInfId()));
            }
            sysInfs.forEach(s->{
                SysApiInf sysApiInf=new SysApiInf();
                sysApiInf.setId(IdUtil.getSnowflake().nextId());
                sysApiInf.setSysApiId(sysApi.getApiId());
                sysApiInf.setSysInfId(s.getInfId());
                sysApiInfMapper.insert(sysApiInf);
            });
        }
        return R.ok(sysApi);
    }

}
