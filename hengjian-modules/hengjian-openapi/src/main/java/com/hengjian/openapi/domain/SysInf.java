package com.hengjian.openapi.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用接口对象 sys_inf
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_inf")
public class SysInf extends BaseEntity {

    private static final long serialVersionUID = 12357157107350L;

    /**
     * 主键ID
     */
    @TableId(value = "inf_id")
    private Long infId;


    /**
     * 接口名称
     */
    private String infName;

    /**
     * 1:第三方请求数据2:向第三方推送数据
     */
    private Integer infType;

    /**
     * 接口正式环境地址
     */
    private String infUrl;

    /**
     * 接口测试环境地址
     */
    private String infTestUrl;

    /**
     * 接口冗余参数(json)
     */
    private String infParameters;

    /**
     * 接口备注
     */
    private String infNote;

    /**
     * 请求类型 1:Post 2.Get
     */
    private Integer requestType;

    /**
     * 请求地址用测试还是正式 1测试 2正式
     */
    private Integer isTest;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;


}
