package com.hengjian.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.SysApi;
import com.hengjian.openapi.domain.bo.SysApiBo;
import com.hengjian.openapi.domain.vo.SysApiVo;
import com.hengjian.openapi.mapper.SysApiMapper;
import com.hengjian.openapi.service.ISysApiService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 应用配置Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-05-13
 */
@RequiredArgsConstructor
@Service
public class SysApiServiceImpl implements ISysApiService {

    private final SysApiMapper baseMapper;

    /**
     * 查询应用配置
     */
    @Override
    public SysApiVo queryById(Long apiId){
        return baseMapper.selectVoById(apiId);
    }

    /**
     * 查询应用配置列表
     */
    @Override
    public TableDataInfo<SysApiVo> queryPageList(SysApiBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysApi> lqw = buildQueryWrapper(bo);
        Page<SysApiVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询应用配置列表
     */
    @Override
    public List<SysApiVo> queryList(SysApiBo bo) {
        LambdaQueryWrapper<SysApi> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysApi> buildQueryWrapper(SysApiBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysApi> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApiKey()), SysApi::getApiKey, bo.getApiKey());
        lqw.eq(StringUtils.isNotBlank(bo.getApiSecretkey()), SysApi::getApiSecretkey, bo.getApiSecretkey());
        lqw.like(StringUtils.isNotBlank(bo.getApiName()), SysApi::getApiName, bo.getApiName());
        return lqw;
    }

    /**
     * 新增应用配置
     */
    @Override
    public Boolean insertByBo(SysApiBo bo) {
        SysApi add = MapstructUtils.convert(bo, SysApi.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setApiId(add.getApiId());
        }
        return flag;
    }

    /**
     * 修改应用配置
     */
    @Override
    public Boolean updateByBo(SysApiBo bo) {
        SysApi update = MapstructUtils.convert(bo, SysApi.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysApi entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除应用配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        //先查询
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SysApi getSysApiByTenantId(String tenantId) {
        LambdaQueryWrapper<SysApi> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysApi::getTenantId,tenantId)
            .eq(SysApi::getDelFlag,0);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public boolean getSysConfigByTenantIdAllowPush(String tenantId) {

        String allowTenantId = baseMapper.getSysConfigByKey("order.cancellation.automatic");
        if (StringUtils.isBlank(allowTenantId)) {
            return false;
        }
        String[] split = allowTenantId.split("[，,;]");
        for (String s : split) {
            if (s.equals(tenantId)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<String> getSysConfigByTenantIdAllowPush() {
        String allowTenantId = baseMapper.getSysConfigByKey("order.cancellation.automatic");
        if (StringUtils.isBlank(allowTenantId)) {
            return null;
        }
        String[] split = allowTenantId.split("[，,;]");
        return Arrays.asList(split);
    }

    /**
     * 功能描述：内部是sysconfig表的isNotExist方法这里是线上着急,避免引用其他模块循环以来的情况临时写sql
     *
     * @param tenantId 租户id
     * @return boolean
     * <AUTHOR>
     * @date 2025/04/18
     */
    @Override
    public boolean isNotExist(String tenantId) {

        return !TenantHelper.ignore(()->getSysConfigByTenantIdAllowPush(tenantId));
    }

    @Override
    public List<SysApi> list() {
        return TenantHelper.ignore(()->baseMapper.selectList());
    }
}
