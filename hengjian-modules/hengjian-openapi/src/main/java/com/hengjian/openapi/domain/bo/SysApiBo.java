package com.hengjian.openapi.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.openapi.domain.SysApi;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 应用配置业务对象 sys_api
 *
 * <AUTHOR> Li
 * @date 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysApi.class, reverseConvertGenerate = false)
public class SysApiBo extends BaseEntity {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空", groups = { EditGroup.class })
    private Long apiId;

    @NotNull(message = "租户ID不能为空", groups = { AddGroup.class,EditGroup.class })
    private String  tenantId;
    /**
     * 应用Key
     */
    @NotBlank(message = "应用Key不能为空", groups = { AddGroup.class, EditGroup.class })
    private String apiKey;

    /**
     * 应用密钥
     */
    @NotBlank(message = "应用密钥不能为空", groups = { AddGroup.class, EditGroup.class })
    private String apiSecretkey;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String apiName;


}
