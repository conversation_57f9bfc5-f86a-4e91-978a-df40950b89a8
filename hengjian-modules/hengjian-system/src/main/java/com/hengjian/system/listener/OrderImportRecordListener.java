package com.hengjian.system.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.hengjian.common.excel.core.ExcelListener;
import com.hengjian.common.excel.core.ExcelResult;
import com.hengjian.system.domain.vo.SysUserImportVo;
import com.zsmall.order.entity.domain.OrderImportRecord;
import lombok.extern.slf4j.Slf4j;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/1 10:45
 */
@Slf4j
public class OrderImportRecordListener extends AnalysisEventListener<OrderImportRecord> implements ExcelListener<OrderImportRecord> {
    @Override
    public ExcelResult<OrderImportRecord> getExcelResult() {
        return null;
    }

    @Override
    public void invoke(OrderImportRecord data, AnalysisContext context) {

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
